{% if is_admin %}
    {% extends "header_admin.html" %}
{% else %}
    {% extends "header.html" %}
{% endif %}

{% block content %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URLCHECK - Detailed Analysis</title>
    <style>
        /* Reset and base styles */
        body, h1, h2, h3, p, table {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        body {
    background: linear-gradient(135deg, #080b0e 0%, #1c2437 100%, #0f1419 100%) fixed;
    background-size: cover;
    color: #ffffff;
    min-height: 100vh;
}

        /* Main container - standardized */
        .dashboard-container {
            max-width: 1200px;
            margin: 40px auto;
            padding: 0 20px;
            margin-bottom: 100px;
        }

        /* Header section */
        .scan-header {
            background-color: #282828;
            color: white;
            border-radius: 8px;
            padding: 25px 30px;
            margin-bottom: 30px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        }

        .scan-header h1 {
            font-size: 24px;
            margin: 0;
            font-weight: 700;
            letter-spacing: -0.5px;
            color: #5dcef0; /* Spotify green */
        }

        .scan-header h2 {
            font-size: 16px;
            margin: 10px 0 0;
            font-weight: 400;
            word-break: break-all;
            opacity: 0.7;
        }

        /* Card styling */
        .card {
            background: #181818;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
            margin-bottom: 25px;
            overflow: hidden;
        }

        .card-header {
            background: #282828;
            padding: 15px 20px;
            border-bottom: 1px solid #333333;
        }

        .card-header h3 {
            margin: 0;
            color: #ffffff;
            font-size: 18px;
            font-weight: 600;
        }

        .card-body {
            padding: 20px;
            color: #b3b3b3;
        }

        /* Info grid */
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 15px;
        }

        .info-item {
            padding: 15px;
            border-radius: 6px;
            background-color: #282828;
        }

        .info-item strong {
            display: block;
            color: #ffffff;
            margin-bottom: 5px;
            font-size: 14px;
            font-weight: 500;
        }

        .info-item span {
            font-size: 15px;
            word-break: break-all;
            color: #b3b3b3;
        }

        /* Classification badge */
        .badge {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 5px;
            font-weight: 600;
            font-size: 14px;
            text-align: center;
            width: 90%;
            color: #000000 !important;
        }

        .badge-safe {
            background-color: #1DB954; /* Spotify green */
            color: #000000;
        }

        .badge-suspicious {
            background-color: #E91429; /* Spotify red */
            color: #000000;
        }

        /* Table styling */
        .breakdown-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }

        .breakdown-table th {
            background-color: #282828;
            color: #ffffff;
            font-weight: 600;
            text-align: left;
            padding: 12px 15px;
            border-bottom: 2px solid #333333;
        }

        .breakdown-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #333333;
            vertical-align: top;
            color: #b3b3b3;
        }

        .breakdown-table tr:last-child td {
            border-bottom: none;
        }

        .breakdown-table tr:hover {
            background-color: #222222;
        }

        /* Standardized Mobile Responsive Design */
        @media (max-width: 768px) {
            .dashboard-container {
                margin: 20px auto;
                padding: 0 20px;
                margin-bottom: 120px;
            }

            .scan-header {
                padding: 20px 25px;
                margin-bottom: 30px;
                margin-top: 50px;
            }

            .scan-header h1 {
                font-size: 32px;
                margin-bottom: 15px;
            }

            .scan-header h2 {
                font-size: 18px;
            }

            .info-grid {
                grid-template-columns: 1fr;
            }

            .breakdown-table, .breakdown-table thead, .breakdown-table tbody,
            .breakdown-table th, .breakdown-table td, .breakdown-table tr {
                display: block;
            }

            .breakdown-table thead tr {
                position: absolute;
                top: -9999px;
                left: -9999px;
            }

            .breakdown-table tr {
                border: 1px solid #333333;
                margin-bottom: 15px;
                border-radius: 6px;
                overflow: hidden;
            }

            .breakdown-table td {
                border: none;
                border-bottom: 1px solid #333333;
                position: relative;
                padding-left: 50%;
                text-align: right;
            }

            .breakdown-table td:last-child {
                border-bottom: 0;
            }

            .breakdown-table td:before {
                position: absolute;
                top: 12px;
                left: 15px;
                width: 45%;
                padding-right: 10px;
                white-space: nowrap;
                text-align: left;
                font-weight: bold;
                content: attr(data-label);
                color: #ffffff;
            }
        }

        @media (max-width: 600px) {
            .dashboard-container {
                margin: 15px auto;
                padding: 0 15px;
                margin-bottom: 100px;
            }

            .scan-header {
                padding: 15px 20px;
                margin-bottom: 20px;
            }

            .scan-header h1 {
                font-size: 28px;
                margin-bottom: 12px;
            }

            .scan-header h2 {
                font-size: 16px;
            }

            .card {
                margin-bottom: 15px;
            }

            .card-header h3 {
                font-size: 18px;
            }

            .breakdown-table td {
                padding-left: 45%;
                font-size: 14px;
            }

            .breakdown-table td:before {
                font-size: 13px;
                top: 10px;
                left: 12px;
            }
        }

        @media (max-width: 480px) {
            .dashboard-container {
                margin: 10px auto;
                padding: 0 10px;
                margin-bottom: 80px;
            }

            .scan-header {
                padding: 12px 15px;
                margin-bottom: 15px;
            }

            .scan-header h1 {
                font-size: 24px;
                margin-bottom: 10px;
            }

            .scan-header h2 {
                font-size: 14px;
            }

            .card {
                margin-bottom: 12px;
            }

            .card-header h3 {
                font-size: 16px;
            }

            .breakdown-table td {
                padding-left: 40%;
                font-size: 13px;
            }

            .breakdown-table td:before {
                font-size: 12px;
                top: 8px;
                left: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- Header Section -->
        <div class="scan-header">
            <h1>URL Scan Analysis</h1>
            <h2>{{ scan_url }}</h2>
        </div>

        {% if scan %}
        <!-- Summary Card -->
        <div class="card">
            <div class="card-header">
                <h3>Scan Summary</h3>
            </div>
            <div class="card-body">
                <div class="info-grid">
                    <div class="info-item">
                        <strong>Final Result</strong>
                        <span class="badge {% if scan.classification == 'Safe' %}badge-safe{% else %}badge-suspicious{% endif %}">
                            {{ scan.classification }}
                        </span>
                    </div>
                    <div class="info-item">
                        <strong>ML Prediction</strong>
                        <span class="badge {% if ml_prediction == 'Safe' %}badge-safe{% else %}badge-suspicious{% endif %}">
                            {{ ml_prediction }}
                        </span>
                    </div>
                    <div class="info-item">
                        <strong>Domain</strong>
                        <span>{{ scan.domain }}</span>
                    </div>
                    <div class="info-item">
                        <strong>IP Address</strong>
                        <span>{{ scan.ip_address }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Note Card -->
        <div class="card">
            <div class="card-header">
                <h3>Note</h3>
            </div>
            <div class="card-body">
                <p>{{ note }}</p>
            </div>
        </div>

        <!-- Details Card -->
        <div class="card">
            <div class="card-header">
                <h3>Domain Information</h3>
            </div>
            <div class="card-body">
                <div class="info-grid">
                    <div class="info-item">
                        <strong>Protocol</strong>
                        <span>{{ scan.protocol }}</span>
                    </div>
                    <div class="info-item">
                        <strong>Created</strong>
                        <span>{{ scan.creation_date | format_date }}</span>
                    </div>
                    <div class="info-item">
                        <strong>Updated</strong>
                        <span>{{ scan.updated_date | format_date }}</span>
                    </div>
                    <div class="info-item">
                        <strong>Expiry</strong>
                        <span>{{ scan.expiry_date | format_date }}</span>
                    </div>
                    <div class="info-item">
                        <strong>Registrar</strong>
                        <span>{{ scan.registrar }}</span>
                    </div>
                    <div class="info-item">
                        <strong>URL Length</strong>
                        <span>{{ scan.url_length }}</span>
                    </div>
                    <div class="info-item">
                        <strong>Age</strong>
                        <span>{{ scan.age }}</span>
                    </div>
                    <div class="info-item">
                        <strong>Sender</strong>
                        <span>{{ scan.sender }}</span>
                    </div>
                </div>
            </div>
        </div>

        

        <!-- URL Breakdown Card -->
        <div class="card">
            <div class="card-header">
                <h3>URL Component Analysis</h3>
            </div>
            <div class="card-body">
                <table class="breakdown-table">
                    <thead>
                        <tr>
                            <th>Component</th>
                            <th>Value</th>
                            <th>Analysis</th>
                            <th>Recommendation</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in features %}
                        <tr>
                            <td data-label="Component">{{ item.part }}</td>
                            <td data-label="Value">{{ item.fragment_value }}</td>
                            <td data-label="Analysis">{{ item.reason }}</td>
                            <td data-label="Recommendation">{{ item.mitigation }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        {% else %}
        <div class="card">
            <div class="card-body">
                <p>No scan result available for this URL.</p>
            </div>
        </div>
        {% endif %}
    </div>

</body>
</html>
{% endblock %}

if __name__ == '__main__':
    app.run(debug=True)