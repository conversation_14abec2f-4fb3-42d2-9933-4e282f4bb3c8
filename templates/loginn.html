<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>URLCHECK</title>
    <link rel="icon" href="{{ url_for('static', filename='favicon.ico') }}" type="image/x-icon">
  <link rel="shortcut icon" href="favicon.ico" type="image/x-icon">
  <style>
    

    .container {
      max-width: 400px;
      margin: 40px auto;
      padding: 20px;
      border-radius: 10px;
    }

    h2 {
      text-align: center;
      color: #000000;
    }

    p {
      font-size: 14px;
      margin-bottom: 15px;
    }

    ul.criteria {
      list-style-type: none;
      padding: 0;
      margin-bottom: 20px;
    }

    ul.criteria li {
      margin-bottom: 8px;
      color: red;
    }

    ul.criteria li.valid {
      color: #00ff88;
    }

    input[type="password"] {
      width: 100%;
      padding: 10px;
      margin: 8px 0;
      background: #2b2b2b;
      color: #ffffff;
      border: 1px solid #444;
      border-radius: 5px;
    }

    button {
      width: 100%;
      padding: 12px;
      background-color: #d32f2f;
      color: #000000;
      border: none;
      border-radius: 5px;
      font-size: 16px;
      cursor: pointer;
      margin-top: 10px;
    }

    button:disabled {
      background-color: #888;
      cursor: not-allowed;
    }

    .link {
      text-align: center;
      margin-top: 15px;
      font-size: 14px;
    }

    .link a {
      color: #e53935;
      text-decoration: none;
    }

    .link a:hover {
      text-decoration: underline;
    }
  </style>
</head>
<body>
  <div class="container">
   
    <ul class="criteria">
      <li id="uppercase">At least 1 <span>uppercase lxcxcetter (A-Z)</span></li>
      <li id="lowercase">At least 1 <span>lowercase letter (a-z)</span></li>
      <li id="digit">At least 1 <span>digit (0-9)</span></li>
      <li id="symbol">At least 1 <span>symbol (!@#$%^&*)</span></li>
      <li id="length">At least <span>12 characters</span></li>
    </ul>

    <input type="password" id="password" placeholder="Enter new password" oninput="validatePassword()"/>
    <input type="password" id="confirmPassword" placeholder="Confirm new password"/>

    <button id="submitBtn" onclick="submitPassword()" disabled>Submit</button>

    
  </div>

  <script>
    function validatePassword() {
      const password = document.getElementById("password").value;
      const submitBtn = document.getElementById("submitBtn");

      // Regular Expressions
      const hasUpper = /[A-Z]/.test(password);
      const hasLower = /[a-z]/.test(password);
      const hasDigit = /[0-9]/.test(password);
      const hasSymbol = /[!@#$%^&*]/.test(password);
      const isLongEnough = password.length >= 12;

      // Toggle valid/invalid classes
      document.getElementById("uppercase").classList.toggle("valid", hasUpper);
      document.getElementById("lowercase").classList.toggle("valid", hasLower);
      document.getElementById("digit").classList.toggle("valid", hasDigit);
      document.getElementById("symbol").classList.toggle("valid", hasSymbol);
      document.getElementById("length").classList.toggle("valid", isLongEnough);

      // Enable button only if all conditions are met
      const allValid = hasUpper && hasLower && hasDigit && hasSymbol && isLongEnough;
      submitBtn.disabled = !allValid;
    }

    function submitPassword() {
      const password = document.getElementById("password").value;
      const confirmPassword = document.getElementById("confirmPassword").value;

      if (password !== confirmPassword) {
        alert("Passwords do not match!");
        return;
      }

      alert("Password reset successfully!");
      // Add real password reset handling here (API call etc)
    }
  </script>
</body>
</html>
