{% if is_admin %}
    {% extends "header_admin.html" %}
{% else %}
    {% extends "header.html" %}
{% endif %}

{% block content %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>URLCHECK</title>
    <link rel="icon" href="{{ url_for('static', filename='favicon.ico') }}" type="image/x-icon">
    <style>
        /* Reset and base styles */
        body, h1, h2, p, div {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        /* Dark mode base styles */
body {
    background: linear-gradient(135deg, #080b0e 0%, #1c2437 100%, #0f1419 100%) fixed;
    background-size: cover;
    color: #ffffff;
    min-height: 100vh;
}

/* Input field styling for dark mode */
input[type="text"],
input[type="text"]:focus,
input[type="text"]:active,
input[type="text"]:hover,
input[type="password"],
input[type="password"]:focus,
input[type="email"],
input[type="email"]:focus,
textarea,
textarea:focus,
select,
select:focus {
    background-color: #333333 !important;
    color: #ffffff !important;
    border: 1px solid #444444;
    outline-color: #1e3c72;
}

/* Override browser autofill styles which often use white backgrounds */
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
    -webkit-box-shadow: 0 0 0 30px #333333 inset !important;
    -webkit-text-fill-color: #ffffff !important;
    transition: background-color 5000s ease-in-out 0s;
}

        /* Main content container */

        /* Main container */
        .container {
            max-width: 1200px;
            margin: 40px auto;
            padding: 0 20px;
            margin-bottom: 210px;
        }

        /* Touch-friendly improvements */
        @media (hover: none) and (pointer: coarse) {
            form input[type="text"] {
                min-height: 48px;
                font-size: 16px; /* Prevents zoom on iOS */
            }

            form input[type="submit"] {
                min-height: 48px;
                font-size: 16px;
            }

            .nav-links a {
                min-height: 44px;
                display: flex;
                align-items: center;
            }
        }

        /* Page title */
        .page-title {
            font-size: 48px;
            font-weight: 900;
            color: #ffffff;
            margin-bottom: 40px;
            text-align: center;
            letter-spacing: -0.04em;
        }



        .content p {
            color: #b3b3b3;
            font-size: 14px;
            margin-bottom: 30px;
            text-align: center;
            
        }

        /* Form styling */
        .scan-form {
            background-color: #282828;
            padding: 25px;
            border-radius: 8px;
            max-width: 710px;
            margin: 0 auto;
        }

        form label {
            display: block;
            margin-bottom: 10px;
            color: #ffffff;
            font-size: 16px;
            font-weight: 500;
            text-align: left;
        }

        form input[type="text"] {
            width: 100%;
            padding: 14px;
            font-size: 16px;
            border: none;
            background-color: #333333;
            color: #ffffff;
            border-radius: 4px;
            margin-bottom: 20px;
            box-sizing: border-box;
            transition: background-color 0.3s;
        }

        form input[type="text"]:focus {
            outline: none;
            background-color: #404040;
            box-shadow: 0 0 0 2px rgba(0, 178, 248, 0.5);
        }

        form input[type="submit"] {
            width: 100%;
            padding: 14px;
            background-color:  #4aa4c0; /* Spotify green */
            color: #000000;
            font-size: 16px;
            font-weight: 700;
            border: none;
            border-radius: 30px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        form input[type="submit"]:hover {
            background-color: #5dcef0;
            transform: scale(1.02);
        }

        /* Flash messages */
        .flash-messages {
            position: fixed;
            top: 85px;
            right: -2px;
            z-index: 9999;
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .flash-messages li {
            background-color: #00b2f880; /* Default Spotify blue */
            color: white;
            padding: 15px 25px;
            margin-top: 10px;
            border-radius: 4px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.4);
            font-size: 14px;
            opacity: 0;
            transform: translateX(100%);
            animation: slideInOut 9s forwards;
            cursor: pointer;
            min-width: 250px;
        }

        /* Success message styling */
        .flash-messages li.success {
            background-color: #00b2f880; /* Spotify blue for success */
        }

        /* Error message styling */
        .flash-messages li.error {
            background-color: #ff0019a8; /* Red with transparency for errors */
        }

        /* Warning message styling */
        .flash-messages li.warning {
            background-color: #ff0019a8; /* Red with transparency for warnings */
        }

        @keyframes slideInOut {
            0% {
                opacity: 0;
                transform: translateX(100%);
            }
            10% {
                opacity: 1;
                transform: translateX(0);
            }
            90% {
                opacity: 1;
                transform: translateX(0);
            }
            100% {
                opacity: 0;
                transform: translateX(100%);
            }
        }

        /* Loading overlay */
        #loading-overlay {
            display: none;
            position: fixed;
            z-index: 100;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            align-items: center;
            justify-content: center;
        }

        #loading-container {
            background-color: #282828;
            width: 280px;
            height: 180px;
            border-radius: 8px;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 15px;
            box-sizing: border-box;
            text-align: center;
        }

        #loading-spinner {
            border: 6px solid #333333;
            border-top: 6px solid #5dcef0; /* Spotify green */
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin-bottom: 15px;
        }

        #loading-message {
            font-size: 16px;
            color: #ffffff;
            font-weight: 600;
        }

        @keyframes spin {
            0% { transform: rotate(0deg);}
            100% { transform: rotate(360deg);}
        }

        /* Standardized Mobile Responsive Design */
        @media (max-width: 768px) {
            .container {
                margin: 20px auto;
                padding: 0 20px;
                margin-bottom: 90px;
            }

            .page-title {
                font-size: 32px;
                margin-bottom: 30px;
                margin-top: 50px;
                text-align: center;
                font-weight: 900;
                letter-spacing: -0.02em;
            }

            .content p {
                font-size: 16px;
                margin-bottom: 25px;
                padding: 0;
            }

            .scan-form {
                padding: 20px;
                margin: 0;
            }

            form input[type="text"] {
                padding: 16px;
                font-size: 16px;
                margin-bottom: 15px;
            }

            form input[type="submit"] {
                padding: 16px;
                font-size: 16px;
            }

            .flash-messages {
                top: 70px;
                right: 20px;
                left: 20px;
            }

            .flash-messages li {
                min-width: auto;
                width: calc(100% - 40px);
                padding: 15px 20px;
                font-size: 14px;
                margin-top: 8px;
            }

            #loading-container {
                width: 260px;
                height: 170px;
            }
        }

        @media (max-width: 600px) {
            .container {
                margin: 15px auto;
                padding: 0 15px;
                margin-bottom: 100px;
            }

            .page-title {
                font-size: 28px;
                margin-bottom: 25px;
                margin-top: 40px;
            }

            .content p {
                font-size: 15px;
                margin-bottom: 20px;
                line-height: 1.5;
            }

            .scan-form {
                padding: 15px;
                margin: 0;
            }

            form input[type="text"] {
                padding: 14px;
                font-size: 16px;
            }

            form input[type="submit"] {
                padding: 14px;
                font-size: 16px;
            }

            .flash-messages {
                top: 60px;
                right: 15px;
                left: 15px;
            }

            .flash-messages li {
                padding: 12px 15px;
                font-size: 13px;
            }

            #loading-container {
                width: 220px;
                height: 150px;
                padding: 10px;
            }

            #loading-message {
                font-size: 14px;
            }
        }

        @media (max-width: 480px) {
            .container {
                margin: 10px auto;
                padding: 0 10px;
                margin-bottom: 80px;
            }

            .page-title {
                font-size: 24px;
                margin-bottom: 20px;
                margin-top: 35px;
            }

            .content p {
                font-size: 14px;
                margin-bottom: 15px;
                line-height: 1.4;
            }

            .scan-form {
                padding: 12px;
                margin: 0;
            }

            form input[type="text"] {
                padding: 12px;
                font-size: 16px;
            }

            form input[type="submit"] {
                padding: 12px;
                font-size: 16px;
            }

            .flash-messages {
                top: 50px;
                right: 10px;
                left: 10px;
            }

            .flash-messages li {
                padding: 10px 12px;
                font-size: 12px;
            }

            #loading-container {
                width: 200px;
                height: 130px;
                padding: 8px;
            }

            #loading-message {
                font-size: 13px;
            }
        }

    </style>
</head>
<body>
<div class="container">
    <div class="content">
        <h1 class="page-title">Scan Now!</h1>
        <p>Check if a link is safe to open with URLCHECK. Protect yourself from suspicious websites and online scams.</p>

        <div class="scan-form">
            <form method="post" action="/result" id="scan-form">
                <input type="text" name="url" id="url" placeholder="https://example.com" required />
                <input type="submit" value="Scan URL" />
            </form>
        </div>
    </div>

        {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
          <ul class="flash-messages" id="flash-messages">
            {% for category, message in messages %}
              <li class="{{ category }}">{{ message }}</li>
            {% endfor %}
          </ul>
        {% endif %}
        {% endwith %}


    <div id="loading-overlay">
        <div id="loading-container">
            <div id="loading-spinner"></div>
            <div id="loading-message">Analyzing URL...</div>
        </div>
    </div>
</div>
    <script>
        // Remove flash message on click
        document.addEventListener('DOMContentLoaded', function() {
            const flashMessages = document.querySelectorAll('.flash-messages li');
            flashMessages.forEach(function(msg) {
                msg.addEventListener('click', function() {
                    msg.style.animation = 'slideOut 0.5s forwards';
                    setTimeout(() => {
                        msg.remove();
                    }, 500);
                });
            });

            // Show loading overlay on form submit
            const form = document.getElementById('scan-form');
            form.addEventListener('submit', function(event) {
                document.getElementById('loading-overlay').style.display = 'flex';
                const submitButton = form.querySelector('input[type="submit"]');
                submitButton.disabled = true;
            });
        });


    </script>
        <p2 style="text-align:center; font-family: Arial, serif; font-size: 11px; color: #535151; position: fixed; bottom: 0px;  margin-bottom: 15px; width: 100%; left: 0px;">© URLCHECK 2025.All rights reserved</p2>

</body>
</html>
{% endblock %}