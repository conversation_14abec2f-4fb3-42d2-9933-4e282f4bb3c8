{% extends "header_login.html" %}
{% block content %}

<!DOCTYPE html>
<html>
<head>
    <title>OTP Verification</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@100;300;400;500;700;900&display=swap" rel="stylesheet">
    <style>
        /* Reset and base styles */
        body, h1, h2, p, div {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        /* Dark mode base styles */
        body {
    background: linear-gradient(135deg, #080b0e 0%, #1c2437 100%, #0f1419 100%) fixed;
    background-size: cover;
    color: #ffffff;
    min-height: 100vh;
}

        /* Input field styling for dark mode */
        input[type="text"],
        input[type="text"]:focus,
        input[type="text"]:active,
        input[type="text"]:hover,
        input[type="password"],
        input[type="password"]:focus,
        input[type="email"],
        input[type="email"]:focus,
        textarea,
        textarea:focus,
        select,
        select:focus {
            background-color: #333333 !important;
            color: #ffffff !important;
            border: 1px solid #444444;
            outline-color: #1e3c72;
        }

        /* Override browser autofill styles which often use white backgrounds */
        input:-webkit-autofill,
        input:-webkit-autofill:hover,
        input:-webkit-autofill:focus,
        input:-webkit-autofill:active {
            -webkit-box-shadow: 0 0 0 30px #333333 inset !important;
            -webkit-text-fill-color: #ffffff !important;
            transition: background-color 5000s ease-in-out 0s;
        }

        /* Main container */
        .container {
            max-width: 1200px;
            margin: 40px auto;
            padding: 0 20px;
        }

        /* Page title */
        .page-title {
            font-family: 'Roboto', sans-serif;
            font-size: 48px;
            font-weight: 900;
            color: #ffffff;
            margin-bottom: 30px;
            text-align: center;
            letter-spacing: -0.04em;
        }

        /* Description text styling */
        .otp-description {
            text-align: center;
            color: #b3b3b3;
            font-size: 16px;
            margin: 20px auto;
            max-width: 600px;
            line-height: 1.5;
        }

        /* OTP Form Styles */
        .otp-form {
            display: flex;
            flex-direction: column;
            width: 30%;
            margin: auto;
            margin-top: 20px;
        }

        .otp-form label {
            font-weight: 500;
            margin-bottom: 5px;
            color: #b3b3b3;
            font-size: 14px;
        }

        .otp-form input[type="text"] {
            padding: 10px 12px;
            background-color: #282828;
            border: 1px solid #333;
            border-radius: 30px;
            margin-bottom: 18px;
            font-size: 16px;
            color: #ffffff;
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
            text-align: center;
            letter-spacing: 8px;
            box-sizing: border-box;
        }

        .otp-form input[type="text"]:focus {
            border-color: #5dcef0;
            box-shadow: 0 0 0 2px rgba(29, 185, 84, 0.2);
            outline: none;
        }

        .otp-form button {
            margin-top: 10px;
            padding: 12px;
            background-color: #4aa4c0;
            color: rgb(0, 0, 0);
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.3s ease, transform 0.2s ease;
        }

        .otp-form button:hover {
            background-color: #5dcef0;
            transform: scale(1.02);
        }

        /* Flash messages container */
        .flash-messages {
            position: fixed;
            top: 85px;
            right: -2px;
            z-index: 9999;
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .flash-messages li {
            background-color: #ff0019a8;
            color: white;
            padding: 15px 25px;
            margin-top: 10px;
            border-radius: 4px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.4);
            font-size: 14px;
            opacity: 0;
            transform: translateX(100%);
            animation: slideInOut 5s forwards;
            cursor: pointer;
            min-width: 250px;
        }

        /* Animation for slide in from right and fade out */
        @keyframes slideInOut {
            0% {
                opacity: 0;
                transform: translateX(100%);
            }
            10% {
                opacity: 1;
                transform: translateX(0);
            }
            90% {
                opacity: 1;
                transform: translateX(0);
            }
            100% {
                opacity: 0;
                transform: translateX(100%);
            }
        }

        /* Responsive design */
        @media (max-width: 600px) {
            .container {
                padding: 20px 15px;
                margin: 20px auto;
            }

            .page-title {
                font-size: 32px;
            }

            .otp-description {
                font-size: 14px;
                margin: 15px auto;
                padding: 0 10px;
            }

            .otp-form {
                width: 90%;
            }

            .otp-form button {
                padding: 12px;
                font-size: 15px;
            }

            .flash-messages li {
                min-width: 200px;
                padding: 12px 20px;
                font-size: 13px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="page-title">OTP Verification</h1>
        <p class="otp-description">We've sent a verification code to your email. Please check your inbox or spam and enter the code below to complete the verification process.</p>

        {% with messages = get_flashed_messages(category_filter=["error", "success"]) %}
          {% if messages %}
            <ul class="flash-messages" id="flash-messages">
              {% for message in messages %}
                <li>{{ message }}</li>
              {% endfor %}
            </ul>
          {% endif %}
        {% endwith %}

        <form method="POST" class="otp-form">
            <label for="otp">Enter your OTP code:</label>
            <input type="text" name="otp" id="otp" maxlength="6" pattern="\d{6}" required autofocus>
            <button type="submit">Verify OTP</button>
        </form>
    </div>

    <!-- Copyright notice -->
    <p2 style="text-align:center; font-family: Arial, serif; font-size: 11px; color: #535151; position: fixed; bottom: 0px;  margin-bottom: 15px; width: 100%; left: 0px;">© URLCHECK 2025.All rights reserved</p2>

    <script>
        // Remove flash message on click
        document.addEventListener('DOMContentLoaded', function() {
            const flashMessages = document.querySelectorAll('.flash-messages li');
            flashMessages.forEach(function(msg) {
                msg.addEventListener('click', function() {
                    msg.style.animation = 'slideOut 0.5s forwards';
                    setTimeout(() => {
                        msg.remove();
                    }, 500);
                });
            });
        });
    </script>
</body>
</html>

{% endblock %}


