{% if is_admin %}
    {% extends "header_admin.html" %}
{% else %}
    {% extends "header.html" %}
{% endif %}

{% block content %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URLCHECK - Subscription Plans</title>
    <style>
        /* Reset and base styles */
        body, h1, h2, p, div {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        body {
    background: linear-gradient(135deg, #080b0e 0%, #1c2437 100%, #0f1419 100%) fixed;
    background-size: cover;
    color: #ffffff;
    min-height: 100vh;
}
        
        /* Main container */
        .container {
            max-width: 1200px;
            margin: 40px auto;
            padding: 0 20px;
            margin-bottom: 59px;
        }
        
        /* Page title */
        .page-title {
            font-size: 48px;
            font-weight: 900;
            color: #ffffff;
            margin-bottom: 40px;
            text-align: center;
            letter-spacing: -0.04em;
        }
        
        /* Pricing card */
        .pricing-card {
            background: #181818;
            border-radius: 8px;
            box-shadow: 0 8px 24px rgba(0,0,0,0.3);
            overflow: hidden;
            display: flex;
            flex-direction: row;
            max-width: 670px;
            max-height: 220px;
            margin: 0 auto;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .pricing-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 30px rgba(0,0,0,0.4);
        }
        
        /* Left side of card */
        .card-left {
            background: #282828;
            color: white;
            padding: 40px 30px;
            width: 40%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
        }
        
        .card-left h1 {
            font-size: 27px;
            margin-bottom: 0px;
            margin-top: 25px;
            font-weight: 700;
            color: #5dcef0; /* Spotify green */
        }
        
        .price {
            font-size: 30px;
            font-weight: 700;

        }
        
        .duration {
            font-size: 16px;
            opacity: 0.7;
            margin-bottom: 8px;
        }
        
        .buy-button {

            width: 100%;
            margin-bottom: 30px;
        }
        
        .buy-button button {
            background-color: #4aa4c0; /* Spotify green */
            color: #000000;
            border: none;
            padding: 10px 20px;
            border-radius: 30px;
            font-size: 16px;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            
        }
        
        .buy-button button:hover {
            background-color: #5dcef0;
            transform: scale(1.05);
        }
        
        .buy-button a {
            text-decoration: none;
            color: #000000;
            display: block;
            width: 100%;
        }
        
        /* Right side of card - features */
        .features {
            padding: 40px 30px;
            width: 60%;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        
        .features div {
            padding: 2px 0;
            font-size: 12px;
            color: #b3b3b3;
            display: flex;
            align-items: center;
            border-bottom: 1px solid #282828;
        }
        
        .features div:last-child {
            border-bottom: none;
        }
        
        .features div:before {
            content: "✓";
            color: #5dcef0; /* Spotify green */
            font-weight: bold;
            margin-right: 15px;
            font-size: 22px;
        }
        
        /* Additional plan info */
        .plan-info {
            max-width: 800px;
            margin: 35px auto 0;
            padding: 25px;
            background-color: #181818;
            border-radius: 8px;
            border-left: 4px solid #5dcef0; /* Spotify green */
            max-height: 80px;
            margin-top: 35px;
        }
        
        .plan-info h2 {
            color: #ffffff;
            margin-bottom: 10px;
            margin-top: -7px;
            font-size: 18px;
            font-weight: 700;
            
        }
        
        .plan-info p {
            color: #b3b3b3;
            font-size: 12.5px;
            line-height: 1.6;
        }
        
        /* Standardized Mobile Responsive Design */
        @media (max-width: 768px) {
            .container {
                margin: 20px auto;
                padding: 0 20px;
                margin-bottom: 150px;
            }

            .page-title {
                font-size: 32px;
                margin-bottom: 30px;
                margin-top: 50px;
                text-align: center;
                font-weight: 900;
                letter-spacing: -0.02em;
            }

            .pricing-card {
                flex-direction: column;
                max-width: 100%;
                max-height: none;
                margin: 0 auto 35px auto;
                border-radius: 20px;
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
                border: 1px solid rgba(255, 255, 255, 0.1);
                overflow: hidden;
            }

            .card-left {
                width: 100%;
                padding: 35px 20px;
                text-align: center;
                border-radius: 0;
                background: linear-gradient(135deg, #2a2a2a 0%, #1e1e1e 100%);
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                min-height: 200px;
                margin-left :-28px;
                
            }

            .card-left h1 {
                font-size: 28px;
                margin: 0 0 20px 0;
                color: #5dcef0;
                font-weight: 700;
                text-align: center;
            }

            .price {
                font-size: 42px;
                margin: 10px 0 5px 0;
                font-weight: 700;
                color: #ffffff;
                text-align: center;
                line-height: 1;
            }

            .duration {
                font-size: 18px;
                margin-bottom: 30px;
                opacity: 0.8;
                color: #c0c0c0;
                text-align: center;
            }

            .buy-button {
                margin-bottom: 0;
                width: 100%;
                display: flex;
                justify-content: center;
                align-items: center;
            }

            .buy-button button {
                padding: 18px 35px;
                font-size: 16px;
                border-radius: 30px;
                font-weight: 700;
                width: auto;
                min-width: 200px;
                max-width: 280px;
                background: linear-gradient(135deg, #5dcef0 0%, #4a9fd1 100%);
                transition: all 0.3s ease;
                display: block;
                margin: 0 auto;
            }

            .buy-button button:hover {
                transform: translateY(-2px);
                box-shadow: 0 6px 20px rgba(93, 206, 240, 0.4);
            }

            .features {
                width: 100%;
                padding: 25px 20px;
                border-radius: 0;
                background: linear-gradient(135deg, #1e1e1e 0%, #2a2a2a 100%);
            }

            .features div {
                font-size: 14px;
                padding: 10px 0;
                margin-bottom: 8px;
                border-bottom: 1px solid rgba(255, 255, 255, 0.1);
                color: #e0e0e0;
            }

            .features div:last-child {
                border-bottom: none;
                margin-bottom: 0;
            }

            .features div:before {
                font-size: 16px;
                margin-right: 15px;
                color: #5dcef0;
            }

            .plan-info {
                max-width: 100%;
                margin: 0 auto;
                padding: 25px 20px;
                max-height: none;
                border-radius: 20px;
                background: linear-gradient(135deg, #1e1e1e 0%, #2a2a2a 100%);
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-left: 4px solid #5dcef0;
            }

            .plan-info h2 {
                font-size: 20px;
                margin-top: 0;
                margin-bottom: 15px;
                color: #ffffff;
            }

            .plan-info p {
                font-size: 14px;
                line-height: 1.7;
                color: #c0c0c0;
            }
        }

        @media (max-width: 600px) {
            .container {
                margin: 20px auto;
                padding: 0 20px;
                margin-bottom: 130px;
            }

            .page-title {
                font-size: 28px;
                margin-bottom: 25px;
                margin-top: 40px;
            }

            .pricing-card {
                margin-bottom: 30px;
                border-radius: 18px;
            }

            .card-left {
                padding: 30px 20px;
                min-height: 180px;
            }

            .card-left h1 {
                font-size: 26px;
                margin: 0 0 18px 0;
            }

            .price {
                font-size: 38px;
                margin: 8px 0 5px 0;
            }

            .duration {
                font-size: 16px;
                margin-bottom: 25px;
            }

            .buy-button button {
                padding: 16px 30px;
                font-size: 15px;
                min-width: 180px;
                max-width: 240px;
            }

            .features {
                padding: 20px;
            }

            .features div {
                font-size: 13px;
                padding: 8px 0;
                margin-bottom: 6px;
            }

            .plan-info {
                padding: 20px;
                border-radius: 18px;
            }

            .plan-info h2 {
                font-size: 18px;
            }

            .plan-info p {
                font-size: 13px;
                line-height: 1.6;
            }
        }

        @media (max-width: 480px) {
            .container {
                margin: 15px auto;
                padding: 0 15px;
                margin-bottom: 120px;
            }

            .page-title {
                font-size: 26px;
                margin-bottom: 20px;
                margin-top: 35px;
            }

            .pricing-card {
                margin-bottom: 25px;
                border-radius: 16px;
            }

            .card-left {
                padding: 25px 15px;
                min-height: 160px;
            }

            .card-left h1 {
                font-size: 24px;
                margin: 0 0 15px 0;
            }

            .price {
                font-size: 34px;
                margin: 8px 0 5px 0;
            }

            .duration {
                font-size: 15px;
                margin-bottom: 22px;
            }

            .buy-button button {
                padding: 14px 25px;
                font-size: 14px;
                min-width: 160px;
                max-width: 220px;
            }

            .features {
                padding: 18px 15px;
            }

            .features div {
                font-size: 12px;
                padding: 7px 0;
                margin-bottom: 5px;
            }

            .features div:before {
                font-size: 14px;
                margin-right: 12px;
            }

            .plan-info {
                padding: 18px 15px;
                border-radius: 16px;
            }

            .plan-info h2 {
                font-size: 17px;
            }

            .plan-info p {
                font-size: 12px;
                line-height: 1.5;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="page-title">Subscription Plan</h1>
        
        <div class="pricing-card">
            <div class="card-left">
                <h1>Business Plan</h1>
                <div class="price">RM 20.90</div>
                <div class="duration">/3months</div>
                <div class="buy-button">
                    <button><a href="https://s.shopee.com.my/8fGFV84HUx?share_channel_code=1" target="_blank">Buy Token</a></button>
                </div>
            </div>
            <div class="features">
                <div>Full access to URLCHECK scanning tools</div>
                <div>Detailed security analysis reports</div>
                <div>Access to URL scanning history</div>
                <div>Mitigation Advice for Detected Threats</div>
                
            </div>
        </div>
        
        <div class="plan-info">
            <h2>Why Choose URLCHECK?</h2>
            <p>URLCHECK offers URL security scanning to safeguard you from data mining, phishing, and other online threats. Powered by cutting-edge machine learning algorithms, our system analyzes URLs to detect suspicious patterns and potential risks—protecting you before you even click.</p>
        </div>
    </div>
    <p2 style="text-align:center; font-family: Arial, serif; font-size: 11px; color: #535151; position: fixed; bottom: 0px;  margin-bottom: 15px; width: 100%; left: 0px;">© URLCHECK 2025.All rights reserved</p2>
</body>
</html>
{% endblock %}