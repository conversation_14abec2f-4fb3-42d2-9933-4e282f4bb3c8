{% extends "header.html" %}
{% block content %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>URLCHECK - Email Verification</title>
    <style>
               /* Reset and base styles */
        body, h1, h2, p, div {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        body {
    background: linear-gradient(135deg, #080b0e 0%, #1c2437 100%, #0f1419 100%) fixed;
    background-size: cover;
    color: #ffffff;
    min-height: 100vh;
}

/* Input field styling for dark mode */
input[type="text"],
input[type="text"]:focus,
input[type="text"]:active,
input[type="text"]:hover,
input[type="password"],
input[type="password"]:focus,
input[type="email"],
input[type="email"]:focus,
textarea,
textarea:focus,
select,
select:focus {
    background-color: #333333 !important;
    color: #ffffff !important;
    border: 1px solid #5dcef0;
    outline-color: #1e3c72;
}

/* Override browser autofill styles which often use white backgrounds */
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
    -webkit-box-shadow: 0 0 0 30px #333333 inset !important;
    -webkit-text-fill-color: #ffffff !important;
    transition: background-color 5000s ease-in-out 0s;
}

        /* Main content container */

        /* Main container */
        .content {
            max-width: 400px;
            margin: 40px auto;
            padding: 0 20px;
            margin-bottom: 225px;
        }

        /* Page title */
        .page-title {
            font-family: 'Poppins', sans-serif;
            font-size: 46.5px;
            font-weight: 900;
            color: #ffffff;
            margin-bottom: 30px;
            text-align: center;
            letter-spacing: -0.04em;

        }

        .page-subtitle {
            color: #a7a7a7;
            font-size: 16px;
            margin-bottom: 32px;
            text-align: center;
            font-weight: 400;
        }

        /* Add this to your existing styles */
    .email-form {
        display: flex;
        flex-direction: row;
        align-items: center;
        gap: 10px;
        margin-bottom: 20px;
    }

    .email_send-form label {
        font-weight: 500;
            margin-bottom: 5px;
            color: #00b2f880;
    }

    .email-form input {
        flex: 1;
        margin-bottom: 0; /* Override the existing margin */
    }

    .email-form button {
        margin-bottom: 0; /* Override the existing margin */
    }

         /* Login Form Styles */
        .otp-form {
            display: flex;
            flex-direction: column;
        }

        .otp-form label {
            font-weight: 500;
            margin-bottom: 5px;
            color: #b3b3b3;
        }

        .email-form input, .otp-form input[type="text"],
        .email-form input, .otp-form input[type="password"] {
            padding: 10px 12px;
            background-color: #282828;
            border: 1px solid #333;
            border-radius: 30px;
            margin-bottom: 18px;
            font-size: 14px;
            color: #ffffff;
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
        }

        /* Center text in OTP input box */
        .otp-form input[type="text"] {
            text-align: center;
        }

        .email-form input:focus, .otp-form input[type="text"]:focus,
        .email-form input:focus, .otp-form input[type="password"]:focus{
            border-color: #5dcef0;
            box-shadow: 0 0 0 2px rgba(29, 185, 84, 0.2);
            outline: none;
        }

        .email-form button, .otp-form button{
            margin-bottom: 20px;
            padding: 12px;
            background-color: #4aa4c0;
            color: rgb(0, 0, 0);
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.3s ease, transform 0.2s ease;
        }

        .email-form button:hover, .otp-form button:hover{
            background-color: #5dcef0;
            transform: scale(1.02);
        }

        /* Flash messages */
        .flash-messages {
            position: fixed;
            top: 85px;
            right: -2px;
            z-index: 9999;
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .flash-messages li {
            background-color: #00b2f880; /* Spotify red */
            color: white;
            padding: 15px 25px;
            margin-top: 10px;
            border-radius: 4px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.4);
            font-size: 14px;
            opacity: 0;
            transform: translateX(100%);
            animation: slideInOut 5s forwards;
            cursor: pointer;
            min-width: 250px;
        }

        @keyframes slideInOut {
            0% {
                opacity: 0;
                transform: translateX(100%);
            }
            10% {
                opacity: 1;
                transform: translateX(0);
            }
            90% {
                opacity: 1;
                transform: translateX(0);
            }
            100% {
                opacity: 0;
                transform: translateX(100%);
            }
        }

        /* Loading overlay */
        #loading-overlay {
            display: none;
            position: fixed;
            z-index: 100;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            align-items: center;
            justify-content: center;
        }

        #loading-container {
            background-color: #282828;
            width: 280px;
            height: 180px;
            border-radius: 8px;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 15px;
            box-sizing: border-box;
            text-align: center;
        }

        #loading-spinner {
            border: 6px solid #333333;
            border-top: 6px solid #5dcef0; /* Spotify green */
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin-bottom: 15px;
        }

        #loading-message {
            font-size: 16px;
            color: #ffffff;
            font-weight: 600;
        }

        @keyframes spin {
            0% { transform: rotate(0deg);}
            100% { transform: rotate(360deg);}
        }

        /* Standardized Mobile Responsive Design */
        @media (max-width: 768px) {
            .content {
                margin: 20px auto;
                padding: 0 20px;
                margin-bottom: 120px;
            }

            .page-title {
                font-size: 32px;
                margin-bottom: 30px;
                margin-top: 50px;
            }

            form input[type="submit"] {
                padding: 14px;
                font-size: 16px;
            }

            .flash-messages li {
                min-width: 250px;
                padding: 15px 20px;
                font-size: 14px;
            }

            #loading-container {
                width: 260px;
                height: 170px;
            }
        }

        @media (max-width: 600px) {
            .content {
                margin: 15px auto;
                padding: 0 15px;
                margin-bottom: 100px;
            }

            .page-title {
                font-size: 28px;
                margin-bottom: 25px;
            }

            form input[type="submit"] {
                padding: 12px;
                font-size: 15px;
            }

            .flash-messages li {
                min-width: 200px;
                padding: 12px 20px;
                font-size: 13px;
            }

            #loading-container {
                width: 240px;
                height: 160px;
            }
        }

        @media (max-width: 480px) {
            .content {
                margin: 10px auto;
                padding: 0 10px;
                margin-bottom: 80px;
            }

            .page-title {
                font-size: 24px;
                margin-bottom: 20px;
            }

            form input[type="submit"] {
                padding: 10px;
                font-size: 14px;
            }

            .flash-messages li {
                min-width: 180px;
                padding: 10px 15px;
                font-size: 12px;
            }

            #loading-container {
                width: 220px;
                height: 150px;
            }
        }
    </style>

</head>
<body>

    <h1 class="page-title">Forgot Password</h1>

    {% with messages = get_flashed_messages() %}
        {% if messages %}
          <ul class="flash-messages" id="flash-messages">
            {% for message in messages %}
              <li>{{ message }}</li>
            {% endfor %}
          </ul>
    {% endif %}
    {% endwith %}

        <label for="email_send">Enter Email:</label>
    <form method="post" action="{{ url_for('send_reset_otp') }}" class="email-form">

        <input type="email" id="email_send" name="email" placeholder="Email" required />
        <button type="submit">Send OTP</button>
    </form>

    <form method="post" action="{{ url_for('confirmation') }}" class="otp-form">
        <label for="otp">Enter OTP:</label>
        <input type="text" id="otp" name="otp" placeholder="OTP" required />
        <button type="submit">Confirm OTP</button>
    </form>
    <p2 style="text-align:center; font-family: Arial, serif; font-size: 11px; color: #535151; position: fixed; bottom: 0px;  margin-bottom: 15px; width: 100%; left: 0px;">© URLCHECK 2025.All rights reserved</p2>

<script>
     // Remove flash message on click
        document.addEventListener('DOMContentLoaded', function() {
            const flashMessages = document.querySelectorAll('.flash-messages li');
            flashMessages.forEach(function(msg) {
                msg.addEventListener('click', function() {
                    msg.style.animation = 'slideOut 0.5s forwards';
                    setTimeout(() => {
                        msg.remove();
                    }, 500);
                });
            });

            // Show loading overlay on form submit and prevent multiple submits
            const form = document.getElementById('scan-form');
            form.addEventListener('submit', function(event) {
                // Show loading overlay
                document.getElementById('loading-overlay').style.display = 'flex';

                // Disable submit button to prevent multiple submits
                const submitButton = form.querySelector('input[type="submit"]');
                submitButton.disabled = true;
            });
        });
</script>

</body>
</html>
{% endblock %}
