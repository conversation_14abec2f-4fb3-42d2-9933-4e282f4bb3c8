<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>URLCHECK</title>
  <link rel="icon" href="{{ url_for('static', filename='admin_favicon.ico') }}" type="image/x-icon">
     <style>
/* Base Styles */

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f4f4f9;
    color: #333;
    line-height: 2;
}

/* Header */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(90deg, #000000, #000000);
    padding: 15px 20px;
    color: white;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    position: relative;
}

.header .logo {
    font-size: 26px;
    font-weight: bold;
}

.header .logo a {
    color: #ffffff;
    text-decoration: none;
    transition: color 0.3s ease;
}

.header .logo a:hover {
    color: #ffffff;
}

/* Navigation */
.nav-links {
    display: flex;
    gap: 15px;
    align-items: center;
}

.nav-links a {
    color: #ffffff;
    text-decoration: none;
    font-size: 16px;
    padding: 8px 14px;

    transition: background-color 0.3s ease, color 0.3s ease;
}

.nav-links a:hover {
    background-color: #000000;
    color: #d19af3;
}

/* Hamburger Icon */
.menu-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 5px;
}

.menu-toggle div {
    width: 25px;
    height: 3px;
    background-color: white;
    transition: all 0.3s ease;
}

/* Mobile Dropdown */
@media (max-width: 768px) {
    .nav-links {
        display: none;
        flex-direction: column;
        height: 245px;
        background-color: #000000;
        position: fixed;
        top: 65px;
        right: 0;
        width: 200px;
        z-index: 999;
        border-bottom-left-radius: 20px
    }

    .nav-links a {
        padding: 12px;
        border-top: 1px solid #000000;
    }

    .menu-toggle {
        display: flex;
    }

    .nav-links.active {
        display: flex;
    }
}

/* Main Content */


/* Extra small screens */
@media (max-width: 480px) {
    .header .logo {
        font-size: 22px;
    }
    .content h1 {
        font-size: 26px;
    }
    .content p {
        font-size: 16px;
    }
}

.profile-dropdown {
    position: relative;
    display: inline-block;
}

.profile-button {
    background: none;
    border: none;
    color: white;
    font-size: 16px;
    cursor: pointer;
    display: flex;
    align-items: center;
}

.mini-profile-pic {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    object-fit: cover;
    margin-right: 8px;
}

#dropdown-content {
    display: none;
    position: absolute;
    right: 0px;
    background-color:#000000; /* black */
    min-width: 220px;
    box-shadow: 0px 8px 16px rgba(0,0,0,0.2);
    border-radius: 10px;
    overflow: hidden;
    z-index: 1;
    text-align: center;
    padding: 15px;
}

.large-profile-pic {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    margin-bottom: 10px;
}

.signout-button {
    margin-top: 15px;
    padding: 8px 16px;
    background: white;
    border: 1px solid #ccc;
    border-radius: 8px;
    cursor: pointer;
}

.signout-button:hover {
    background-color: #f2f2f2;
}


  </style>
  <script>
    function toggleDropdown() {
        var dropdown = document.getElementById("dropdown-content");
        dropdown.style.display = (dropdown.style.display === "block") ? "none" : "block";
    }
    
    // Hide dropdown if clicked outside
    window.onclick = function(event) {
      if (!event.target.matches('.profile-button') && !event.target.closest('.profile-dropdown')) {
        var dropdowns = document.getElementsByClassName("dropdown-content");
        for (var i = 0; i < dropdowns.length; i++) {
          var openDropdown = dropdowns[i];
          if (openDropdown.style.display === "block") {
            openDropdown.style.display = "none";
          }
        }
      }
    }
    </script>
</head>
<body>

<div class="header">
    <div class="logo">
        <a href="/dashboard_admin">URLCHECK</a>
    </div>

    <!-- Hamburger Icon -->
    <div class="menu-toggle" onclick="toggleMenu()">
        <div></div>
        <div></div>
        <div></div>
    </div>

    <!-- Navigation Links -->
    <div class="nav-links" id="navLinks">
            <a href="/">Scan</a>

            {% if 'username' in session %}
            <div class="profile-dropdown">
                <button class="profile-button" onclick="toggleDropdown()">
                    {% if profile_picture %}
                    <img src="data:image/jpeg;base64,{{ profile_picture | b64encode }}" alt="Profile Picture" class="mini-profile-pic">
                  {% else %}
                    <img src="{{ url_for('static', filename='default_profile.png') }}" alt="Default Profile" class="mini-profile-pic">
                  {% endif %}
                  {{ username }}
                </button>
            
                <div id="dropdown-content" class="dropdown-content">
                  <div class="profile-info">
                    {% if profile_picture %}
                    <img src="data:image/jpeg;base64,{{ profile_picture | b64encode }}" alt="Profile Picture" class="large-profile-pic">
                  {% else %}
                    <img src="{{ url_for('static', filename='default_profile.png') }}" alt="Default Profile" class="large-profile-pic">
                  {% endif %}
                  </div>
                  <a href="/admin_profile">Edit Profile</a>
                  <a href="/logout">Logout</a>
                </div>
              </div>
                 
            {% else %}

            <a href="{{ url_for('signin_page') }}">User Login</a>
        {% endif %}
    </div>
    
</div>

<div class="content">
    {% block content %}
    <div>
        <h1>{{ page_title }}</h1>
        <p>{{ content }}</p>
    </div>
    {% endblock %}
</div>
<p style="text-align:center; font-family: Arial, serif; font-size: 11px; color: #535151; position: fixed; bottom: 15px; width: 100%;">Copyright © URLCHECK 2025</p>

<script>
function toggleMenu() {
    var navLinks = document.getElementById('navLinks');
    navLinks.classList.toggle('active');
}

// Browser navigation control for authenticated users
{% if 'email' in session or 'username' in session %}
(function() {
    // Check if we're on the main page/index
    var isMainPage = window.location.pathname === '/' || window.location.pathname === '/index';

    if (isMainPage) {
        // For main page: Prevent back navigation, stay on current page

        // Initialize robust history protection for main page
        var currentUrl = window.location.href;
        var stateObj = { page: 'main', timestamp: Date.now() };

        // Create multiple history entries to prevent back navigation
        history.replaceState(stateObj, '', currentUrl);
        for (var i = 0; i < 10; i++) {
            history.pushState(stateObj, '', currentUrl);
        }

        // Counter to track rapid back button clicks
        var backClickCount = 0;
        var lastClickTime = 0;
        var isBlocking = false;

        // Listen for popstate event (back/forward button clicks)
        window.addEventListener('popstate', function(event) {
            var currentTime = Date.now();

            // Prevent any navigation immediately
            event.preventDefault();
            event.stopPropagation();

            // Reset counter if more than 2 seconds have passed
            if (currentTime - lastClickTime > 2000) {
                backClickCount = 0;
            }

            backClickCount++;
            lastClickTime = currentTime;

            // Always push multiple states to stay on current page
            for (var j = 0; j < 5; j++) {
                history.pushState(stateObj, '', currentUrl);
            }

            // If user is clicking rapidly (more than 5 times), add extra protection
            if (backClickCount > 5) {
                if (!isBlocking) {
                    isBlocking = true;

                    // Add many more history entries for persistent clickers
                    for (var k = 0; k < 20; k++) {
                        history.pushState(stateObj, '', currentUrl);
                    }

                    // Reset blocking flag after 3 seconds
                    setTimeout(function() {
                        isBlocking = false;
                        backClickCount = 0;
                    }, 3000);
                }
            }

            // Ensure we're still on the correct page
            if (window.location.href !== currentUrl) {
                window.location.replace(currentUrl);
            }
        });

        // Additional protection: Handle page visibility changes
        document.addEventListener('visibilitychange', function() {
            if (document.visibilityState === 'visible') {
                if (window.location.pathname === '/' || window.location.pathname === '/index') {
                    for (var m = 0; m < 5; m++) {
                        history.pushState(stateObj, '', currentUrl);
                    }
                }
            }
        });

        // Handle window focus events
        window.addEventListener('focus', function() {
            if (window.location.pathname === '/' || window.location.pathname === '/index') {
                for (var n = 0; n < 3; n++) {
                    history.pushState(stateObj, '', currentUrl);
                }
            }
        });

        // Prevent page from being cached
        window.addEventListener('beforeunload', function() {
            for (var p = 0; p < 5; p++) {
                history.pushState(stateObj, '', currentUrl);
            }
        });

    } else {
        // For other authenticated pages: Use logout behavior

        // Listen for popstate event (back/forward button clicks)
        window.addEventListener('popstate', function(event) {
            // User clicked back or forward button - terminate session
            terminateSession('You have been logged out for security reasons. Browser navigation detected.');
        });

        // Listen for page show event (when page becomes visible)
        window.addEventListener('pageshow', function(event) {
            if (event.persisted) {
                terminateSession('You have been logged out for security reasons. Browser navigation detected.');
            }
        });

        // Function to terminate session completely
        function terminateSession(message) {
            sessionStorage.clear();
            localStorage.clear();

            alert(message);

            fetch('/logout', {
                method: 'GET',
                credentials: 'same-origin'
            }).then(function() {
                window.location.replace('/');
            }).catch(function() {
                window.location.replace('/');
            });
        }
    }
})();
{% endif %}
</script>

</body>
</html>
