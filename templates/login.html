{% if is_admin %}
    {% extends "header_admin.html" %}
{% else %}
    {% extends "header_login.html" %}
{% endif %}

{% block content %}

<!DOCTYPE html>
<html>
<head>
    <title>Login</title>
    <style>
        /* Reset and base styles */
        body, h1, h2, p, div {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        /* Dark mode base styles */
body {
    background: linear-gradient(135deg, #080b0e 0%, #1c2437 100%, #0f1419 100%) fixed;
    background-size: cover;
    color: #ffffff;
    min-height: 100vh;
}

/* Input field styling for dark mode */
input[type="text"],
input[type="text"]:focus,
input[type="text"]:active,
input[type="text"]:hover,
input[type="password"],
input[type="password"]:focus,
input[type="email"],
input[type="email"]:focus,
textarea,
textarea:focus,
select,
select:focus {
    background-color: #333333 !important;
    color: #ffffff !important;
    border: 1px solid #444444;
    outline-color: #1e3c72;
}

/* Override browser autofill styles which often use white backgrounds */
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
    -webkit-box-shadow: 0 0 0 30px #333333 inset !important;
    -webkit-text-fill-color: #ffffff !important;
    transition: background-color 5000s ease-in-out 0s;
}

        /* Main content container */

        /* Main container */
        .container {
            max-width: 1200px;
            margin: 40px auto;
            padding: 0 20px;
            margin-bottom: 177px;
        }

        /* Page title */
        .page-title {
            font-family: 'Poppins', sans-serif;
            font-size: 48px;
            font-weight: 900;
            color: #ffffff;
            margin-bottom: 30px;
            text-align: center;
            letter-spacing: -0.04em;

        }

         .content p {
            color: #b3b3b3;
            font-size: 14px;
            margin-bottom: 30px;
            text-align: center;



        }
        /* Login Form Styles */
        .login-form {
            display: flex;
            flex-direction: column;
            width: 30%;
            margin: auto;
            position:center;
        }

        .login-form label {
             font-weight: 600;
            margin-bottom: 8px;
            color: #b3b3b3;
            font-size: 12px;
        }

        .login-form input[type="email"],
        .login-form input[type="password"] {
            padding: 10px 12px;
            background-color: #282828;
            border: 1px solid #333;
            border-radius: 30px;
            margin-bottom: 18px;
            font-size: 14px;
            color: #ffffff;
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
        }

        /* Password input wrapper for eye icon */
        .password-input-wrapper {
            position: relative;
            display: block;
            width: 100%;
        }

        .password-input-wrapper input[type="password"],
        .password-input-wrapper input[type="text"] {
            padding-right: 50px;
            width: 100%;
            box-sizing: border-box;
            padding: 10px 50px 10px 12px;
            background-color: #282828;
            border: 1px solid #333;
            border-radius: 30px;
            margin-bottom: 18px;
            font-size: 14px;
            color: #ffffff;
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
        }

        /* Eye icon styling */
        .password-toggle {
            position: absolute;
            right: 18px;
            top: 50%;
            transform: translateY(-50%);
            margin-top: -9px;
            cursor: pointer;
            color: #b3b3b3;
            transition: color 0.3s ease;
            z-index: 10;
            user-select: none;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
        }

        .password-toggle:hover {
            color: #e0dfdf;
        }

        .password-toggle svg {
            width: 18px;
            height: 18px;
            fill: currentColor;
            display: block;
        }

        .login-form input:focus {
            border-color: #5dcef0;
            box-shadow: 0 0 0 2px rgba(29, 185, 84, 0.2);
            outline: none;
        }

        .login-form button {
            margin-top: 0px;
            padding: 12px;
            background-color: #4aa4c0;
            color: rgb(0, 0, 0);
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.3s ease, transform 0.2s ease;
        }

        .login-form button:hover:enabled {
            background-color: #5dcef0;
            transform: scale(1.02);
        }

        .login-form button:disabled {
            background-color: #666666 !important;
            cursor: not-allowed !important;
            opacity: 0.6 !important;
            transform: none !important;
        }

        .register-link {
            margin-top: 20px;
            text-align: center;
        }

        .register-link a {
            text-decoration: none;
            color: #b3b3b3;
            font-weight: 500;
            transition: color 0.3s ease;
            font-size: 14px;
        }

        .register-link a:hover {
            color: #4aa4c0;
        }

        .forgot-link {
            margin-top: -19px;
            margin-bottom: 18px;
            text-align: right;
        }

        .forgot-link a {
            text-decoration: none;
            color: #b3b3b3;
            font-size: 12px;
            transition: color 0.3s ease;
        }

        .forgot-link a:hover {
            color: #4aa4c0;
        }

        /* Flash messages container */
        .flash-messages {
            position: fixed;
            top: 85px;
            right: -2px;
            z-index: 9999;
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .flash-messages li {
            background-color: #ff0019a8; /* Default red for errors */
            color: white;
            padding: 15px 25px;
            margin-top: 10px;
            border-radius: 4px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.4);
            font-size: 14px;
            opacity: 0;
            transform: translateX(100%);
            animation: slideInOut 5s forwards;
            cursor: pointer;
            min-width: 250px;
        }

        /* Success message styling */
        .flash-messages li.success {
            background-color: #00b2f880; /* Spotify blue for success */
        }

        /* Error message styling */
        .flash-messages li.error {
            background-color: #ff0019a8; /* Red for errors */
        }

        /* Warning message styling */
        .flash-messages li.warning {
            background-color: #ff0019a8; /* Red for warnings */
        }

        /* Animation for slide in from right and fade out */
        @keyframes slideInOut {
            0% {
                opacity: 0;
                transform: translateX(100%);
            }
            10% {
                opacity: 1;
                transform: translateX(0);
            }
            90% {
                opacity: 1;
                transform: translateX(0);
            }
            100% {
                opacity: 0;
                transform: translateX(100%);
            }
        }

        /* Token Expired Modal */
        .modal-overlay {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
            justify-content: center;
            align-items: center;
        }

        .modal-content {
            background-color: #282828;
            padding: 30px;
            border-radius: 12px;
            text-align: center;
            max-width: 400px;
            width: 90%;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.6);
        }

        .modal-content h2 {
            color: #ffffff;
            margin-bottom: 15px;
            font-size: 24px;
            font-weight: 600;
        }

        .modal-content p {
            color: #b3b3b3;
            margin-bottom: 25px;
            font-size: 16px;
            line-height: 1.4;
        }

        .modal-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .modal-btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            min-width: 140px;
        }

        .btn-renew {
            background-color: rgb(74, 164, 192);
            color: #000000;
        }

        .btn-renew:hover {
            background-color: #5dcef0;
            transform: scale(1.05);
        }

        .btn-scan {
            background-color: #404040;
            color: #ffffff;
            border: 1px solid #666666;
        }

        .btn-scan:hover {
            background-color: #505050;
            transform: scale(1.05);
        }

        /* Standardized Mobile Responsive Design */
        @media (max-width: 768px) {
            .container {
                margin: 20px auto;
                padding: 0 20px;
                margin-bottom: 140px;
            }

            .page-title {
                font-size: 32px;
                margin-bottom: 30px;
                margin-top: 50px;
            }

            .login-form {
                width: 80%;
            }

            .login-form button {
                padding: 14px;
                font-size: 16px;
            }

            /* Mobile responsive for password toggle */
            .password-toggle {
                right: 15px;
                width: 22px;
                height: 22px;
                margin-top: -11px;
            }

            .password-toggle svg {
                width: 16px;
                height: 16px;
            }

            .flash-messages li {
                min-width: 250px;
                padding: 15px 20px;
                font-size: 14px;
            }

            .modal-content {
                padding: 30px 25px;
                max-width: 400px;
            }

            .modal-buttons {
                flex-direction: row;
                justify-content: center;
                gap: 15px;
            }

            .modal-btn {
                width: auto;
                min-width: 120px;
            }
        }

        @media (max-width: 600px) {
            .container {
                margin: 15px auto;
                padding: 0 15px;
                margin-bottom: 120px;
            }

            .page-title {
                font-size: 28px;
                margin-bottom: 25px;
            }

            .login-form {
                width: 90%;
            }

            .login-form button {
                padding: 12px;
                font-size: 15px;
            }

            /* Mobile responsive for password toggle */
            .password-toggle {
                right: 12px;
                width: 20px;
                height: 20px;
                margin-top: -10px;
            }

            .password-toggle svg {
                width: 14px;
                height: 14px;
            }

            .flash-messages li {
                min-width: 200px;
                padding: 12px 20px;
                font-size: 13px;
            }

            .modal-content {
                padding: 25px 20px;
                max-width: 350px;
            }

            .modal-buttons {
                flex-direction: column;
                align-items: center;
                gap: 10px;
            }

            .modal-btn {
                width: 100%;
                max-width: 200px;
            }
        }

        @media (max-width: 480px) {
            .container {
                margin: 10px auto;
                padding: 0 10px;
                margin-bottom: 100px;
            }

            .page-title {
                font-size: 24px;
                margin-bottom: 20px;
            }

            .login-form {
                width: 95%;
            }

            .login-form button {
                padding: 10px;
                font-size: 14px;
            }

            /* Mobile responsive for password toggle */
            .password-toggle {
                right: 10px;
                width: 18px;
                height: 18px;
                margin-top: -9px;
            }

            .password-toggle svg {
                width: 12px;
                height: 12px;
            }

            .flash-messages li {
                min-width: 180px;
                padding: 10px 15px;
                font-size: 12px;
            }

            .modal-content {
                padding: 20px 15px;
                max-width: 320px;
            }

            .modal-btn {
                max-width: 180px;
            }
        }
    </style>
</head>
<body>
<div class="container">
    <div class="content">
        <h1 class="page-title">Sign In</h1>

        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
              <ul class="flash-messages" id="flash-messages">
                {% for category, message in messages %}
                  <li class="{{ category }}">{{ message }}</li>
                {% endfor %}
              </ul>
            {% endif %}
        {% endwith %}

        <form method="post" class="login-form" id="loginForm">
            <label for="email">Email:</label>
            <input type="email" name="email" id="email" placeholder="Enter your full name"required>

            <label for="password">Password:</label>
            <div class="password-input-wrapper">
                <input type="password" name="password" id="password" placeholder="Enter your password" required>
                <span class="password-toggle" onclick="togglePasswordVisibility()">
                    <svg id="eyeIcon" viewBox="0 0 24 24">
                        <path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z"/>
                    </svg>
                </span>
            </div>

            <div class="forgot-link">
                <a href="/confirmation">Forgot Password?</a>
            </div>

            <button type="submit" id="signInBtn" {% if cooldown_info and cooldown_info.is_in_cooldown %}disabled{% endif %}>
                {% if cooldown_info and cooldown_info.is_in_cooldown %}
                    Disabled (<span id="cooldown-timer">{{ cooldown_info.remaining_time }}</span>s)
                {% else %}
                    Sign In
                {% endif %}
            </button>
        </form>

        <div class="register-link">
            <a href="{{ url_for('signup_page') }}">Don't have an account? Sign up</a>
        </div>
    </div>

    <!-- Token Expired Modal -->
    {% if token_expired %}
    <div class="modal-overlay" id="tokenExpiredModal" style="display: flex;">
        <div class="modal-content">
            <h2>Account Expired</h2>
            <p>Your account token has expired. You can renew your token to continue using your account, or scan URLs without an account.</p>
            <div class="modal-buttons">
                <a href="/renew_token" class="modal-btn btn-renew">Renew Token</a>
                <a href="/" class="modal-btn btn-scan">Scan Without Account</a>
            </div>
        </div>
    </div>
    {% endif %}

    <p2 style="text-align:center; font-family: Arial, serif; font-size: 11px; color: #535151; position: fixed; bottom: 0px;  margin-bottom: 15px; width: 100%; left: 0px;">© URLCHECK 2025.All rights reserved</p2>


    <script>
        // Toggle password visibility function
        function togglePasswordVisibility() {
            const passwordInput = document.getElementById('password');
            const eyeIcon = document.getElementById('eyeIcon');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                // Change to eye-slash icon (hidden)
                eyeIcon.innerHTML = '<path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94L17.94 17.94zM9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19l-6.37-6.37a2.5 2.5 0 0 0-3.19-3.19L9.9 4.24zM1 1l22 22M8.71 8.71a2.5 2.5 0 1 0 3.54 3.54"/>';
            } else {
                passwordInput.type = 'password';
                // Change back to eye icon (visible)
                eyeIcon.innerHTML = '<path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z"/>';
            }
        }

        // Remove flash message on click and handle modal interactions
        document.addEventListener('DOMContentLoaded', function() {
            const flashMessages = document.querySelectorAll('.flash-messages li');
            flashMessages.forEach(function(msg) {
                msg.addEventListener('click', function() {
                    msg.style.animation = 'slideOut 0.5s forwards';
                    setTimeout(() => {
                        msg.remove();
                    }, 500);
                });
            });

            // Prevent form submission when button is disabled
            const loginForm = document.getElementById('loginForm');
            const signInBtn = document.getElementById('signInBtn');

            if (loginForm) {
                loginForm.addEventListener('submit', function(e) {
                    if (signInBtn && signInBtn.disabled) {
                        e.preventDefault();
                        return false;
                    }
                });
            }

            // Handle token expired modal
            const modal = document.getElementById('tokenExpiredModal');
            if (modal) {
                // Close modal when clicking outside of it
                modal.addEventListener('click', function(event) {
                    if (event.target === modal) {
                        modal.style.display = 'none';
                    }
                });

                // Prevent closing when clicking inside modal content
                const modalContent = modal.querySelector('.modal-content');
                if (modalContent) {
                    modalContent.addEventListener('click', function(event) {
                        event.stopPropagation();
                    });
                }
            }
        });
    </script>

    <!-- Cooldown timer script -->
    {% if cooldown_info and cooldown_info.is_in_cooldown %}
    <script>
        (function() {
            let remainingTime = {{ cooldown_info.remaining_time }};
            const cooldownTimer = document.getElementById('cooldown-timer');
            const signInButton = document.getElementById('signInBtn');

            const countdown = setInterval(function() {
                remainingTime--;
                if (cooldownTimer) {
                    cooldownTimer.textContent = remainingTime;
                }

                if (remainingTime <= 0) {
                    clearInterval(countdown);
                    if (signInButton) {
                        signInButton.disabled = false;
                        signInButton.innerHTML = 'Sign In';
                    }
                    // Remove flash message when cooldown ends
                    const flashMessages = document.querySelectorAll('.flash-messages li');
                    flashMessages.forEach(function(msg) {
                        msg.style.animation = 'slideOut 0.5s forwards';
                        setTimeout(() => {
                            msg.remove();
                        }, 500);
                    });
                }
            }, 1000);
        })();
    </script>
    {% endif %}
</div>
    <!-- Copyright notice -->
</body>
</html>
{% endblock %}
