<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>URLCHECK</title>
  <link rel="icon" href="{{ url_for('static', filename='favicon.ico') }}" type="image/x-icon">

  <style>
/* Base Styles */

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f4f4f9;
    color: #333;
    line-height: 2;
}

/* Header */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(90deg, #000000, #000000);
    padding: 15px 20px;
    color: white;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    position: relative;
}

.header .logo {
    font-size: 26px;
    font-weight: bold;
}

.header .logo a {
    color: #ffffff;
    text-decoration: none;
    transition: color 0.3s ease;
}

.header .logo a:hover {
    color: #ffffff;
}

/* Navigation */
.nav-links {
    display: flex;
    gap: 15px;
    align-items: center;
}

.nav-links a {
    color: #ffffff;
    text-decoration: none;
    font-size: 16px;
    padding: 8px 14px;

    transition: background-color 0.3s ease, color 0.3s ease;
}

.nav-links a:hover {
    background-color: #000000;
    color: #5dcef0;
}

/* Hamburger Icon */
.menu-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 5px;
}

.menu-toggle div {
    width: 25px;
    height: 3px;
    background-color: white;
    transition: all 0.3s ease;
    border-radius: 2px;
}

/* Hamburger animation */
.menu-toggle.active div:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
}

.menu-toggle.active div:nth-child(2) {
    opacity: 0;
}

.menu-toggle.active div:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
}

/* Mobile Dropdown */
@media (max-width: 768px) {
    .nav-links {
        display: none;
        flex-direction: column;
        background-color: #000000;
        position: fixed;
        top: 65px;
        right: 0;
        width: 250px;
        z-index: 1000;
        border-bottom-left-radius: 15px;
        border-top-left-radius: 15px;
        box-shadow: -4px 4px 12px rgba(0, 0, 0, 0.3);
        transform: translateX(100%);
        transition: transform 0.3s ease-in-out;
    }

    .nav-links.active {
        display: flex;
        transform: translateX(0);
    }

    /* Hide profile dropdown in mobile - we'll show items directly */
    .nav-links .profile-dropdown {
        display: none !important;
    }

    /* Show mobile username display */
    .nav-links .mobile-username {
        display: flex !important;
    }

    /* Mobile menu items styling */
    .nav-links a {
        padding: 16px 20px;
        border-bottom: 1px solid #333333;
        font-size: 16px;
        transition: background-color 0.2s ease;
        white-space: nowrap;
        display: flex;
        align-items: center;
    }

    .nav-links a:hover {
        background-color: #333333;
        color: #5dcef0;
    }

    .nav-links a:last-child {
        border-bottom: none;
    }

    /* Mobile username display */
    .mobile-username {
        padding: 16px 20px;
        border-bottom: 1px solid #333333;
        font-size: 16px;
        color: #5dcef0;
        display: flex;
        align-items: center;
    }

    .mobile-username img {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        margin-right: 10px;
    }

    .menu-toggle {
        display: flex;
        z-index: 1001;
    }
}

/* Main Content */


/* Extra small screens */
@media (max-width: 480px) {
    .header {
        padding: 12px 15px;
    }

    .header .logo {
        font-size: 20px;
    }

    .nav-links {
        width: 100%;
        top: 57px;
        border-radius: 0;
        right: 0;
        transform: translateX(100%);
    }

    .nav-links.active {
        transform: translateX(0);
    }

    .content h1 {
        font-size: 24px;
    }

    .content p {
        font-size: 14px;
    }

    .menu-toggle div {
        width: 22px;
        height: 2px;
    }
}

/* Tablet and medium screens */
@media (max-width: 1024px) and (min-width: 769px) {
    .nav-links {
        gap: 12px;
    }

    .nav-links a {
        font-size: 15px;
        padding: 6px 12px;
    }
}

.profile-dropdown {
    position: relative;
    display: inline-block;
}

.profile-button {
    background: none;
    border: none;
    color: white;
    font-size: 16px;
    cursor: pointer;
    display: flex;
    align-items: center;
}

.mini-profile-pic {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    object-fit: cover;
    margin-right: 8px;
}

#dropdown-content {
    display: none;
    position: absolute;
    right: 0px;
    background-color:#000000; /* black */
    min-width: 220px;
    box-shadow: 0px 8px 16px rgba(0,0,0,0.2);
    border-radius: 10px;
    overflow: hidden;
    z-index: 1;
    text-align: center;
    padding: 15px;
}

.large-profile-pic {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    margin-bottom: 10px;
}

.signout-button {
    margin-top: 15px;
    padding: 8px 16px;
    background: white;
    border: 1px solid #ccc;
    border-radius: 8px;
    cursor: pointer;
}

.signout-button:hover {
    background-color: #f2f2f2;
}


  </style>
  <script>
    function toggleDropdown() {
        var dropdown = document.getElementById("dropdown-content");
        dropdown.style.display = (dropdown.style.display === "block") ? "none" : "block";
    }

    // Hide dropdown if clicked outside
    window.onclick = function(event) {
      if (!event.target.matches('.profile-button') && !event.target.closest('.profile-dropdown')) {
        var dropdowns = document.getElementsByClassName("dropdown-content");
        for (var i = 0; i < dropdowns.length; i++) {
          var openDropdown = dropdowns[i];
          if (openDropdown.style.display === "block") {
            openDropdown.style.display = "none";
          }
        }
      }
    }
    </script>
  <style>
/*.footer-credit {
      position: fixed;
      bottom: 0;
      width: 100%;
      background-color: #ffffff;
      color: black;
      text-align: center;
      padding: 10px 0;
      font-size: 14px;
      font-weight: 600;
      font-family: 'Times New Roman', serif;
      box-shadow: 0 -2px 5px rgba(0,0,0,0.2);
      z-index: 1000;
    }*/
    /*.p10{
        font-size: 1px;
    }*/
  </style>
</head>
<body>

<div class="header">
    <div class="logo">
        <a href="/">URLCHECK</a>
    </div>

    <!-- Hamburger Icon -->
    <div class="menu-toggle" onclick="toggleMenu()">
        <div></div>
        <div></div>
        <div></div>
    </div>

    <!-- Navigation Links -->
    <div class="nav-links" id="navLinks">
        <a href="/">Home</a>
        <a href="/plan">Plan</a>
        <a href="/guide">Guide</a>
        <a href="{{ url_for('contact_us_page') }}">Contact Us</a>

        {% if 'email' in session or 'username' in session %}
            <a href="/archiveurl">History</a>

            <!-- Mobile username display (hidden on desktop) -->
            <div class="mobile-username" style="display: none;">
                {% if profile_picture %}
                <img src="data:image/jpeg;base64,{{ profile_picture | b64encode }}" alt="Profile Picture">
                {% else %}
                <img src="{{ url_for('static', filename='profilee.jpg') }}" alt="Default Profile">
                {% endif %}
                <a href="/profile">{{username}}</a>
            </div>

            <!-- Desktop profile dropdown (hidden on mobile) -->
            <div class="profile-dropdown">
                <button class="profile-button" onclick="toggleDropdown()">
                    {% if profile_picture %}
                    <img src="data:image/jpeg;base64,{{ profile_picture | b64encode }}" alt="Profile Picture" class="mini-profile-pic">
                    {% else %}
                    <img src="{{ url_for('static', filename='profilee.jpg') }}" alt="Default Profile" class="mini-profile-pic">
                    {% endif %}
                    {{ username }}
                </button>

                <div id="dropdown-content" class="dropdown-content">
                    <div class="profile-info">
                        {% if profile_picture %}
                        <img src="data:image/jpeg;base64,{{ profile_picture | b64encode }}" alt="Profile Picture" class="large-profile-pic">
                        {% else %}
                        <img src="{{ url_for('static', filename='profilee.jpg') }}" alt="Default Profile" class="large-profile-pic">
                        {% endif %}
                    </div>
                    <a href="/profile">Edit Profile</a>
                    <a href="/logout">Logout</a>
                </div>
            </div>
        {% else %}
            <a href="{{ url_for('signin_page') }}">Login</a>
            <a href="{{ url_for('signup_page') }}">Register</a>
        {% endif %}
    </div>

</div>

<div class="content">

    {% block content %}
    <div>
        <h1>{{ page_title }}</h1>
        <p>{{ content }}</p>
    </div>
    {% endblock %}
</div>

<script>
function toggleMenu() {
    var navLinks = document.getElementById('navLinks');
    var menuToggle = document.querySelector('.menu-toggle');

    navLinks.classList.toggle('active');
    menuToggle.classList.toggle('active');

    // Prevent body scroll when menu is open
    if (navLinks.classList.contains('active')) {
        document.body.style.overflow = 'hidden';
    } else {
        document.body.style.overflow = '';
    }
}

// Close mobile menu when clicking outside
document.addEventListener('click', function(event) {
    var navLinks = document.getElementById('navLinks');
    var menuToggle = document.querySelector('.menu-toggle');
    var header = document.querySelector('.header');

    if (!header.contains(event.target) && navLinks.classList.contains('active')) {
        navLinks.classList.remove('active');
        menuToggle.classList.remove('active');
        document.body.style.overflow = '';
    }
});

// Close mobile menu when window is resized to desktop
window.addEventListener('resize', function() {
    var navLinks = document.getElementById('navLinks');
    var menuToggle = document.querySelector('.menu-toggle');

    if (window.innerWidth > 768 && navLinks.classList.contains('active')) {
        navLinks.classList.remove('active');
        menuToggle.classList.remove('active');
        document.body.style.overflow = '';
    }
});

// Simple and effective back button prevention for main page
{% if 'email' in session or 'username' in session %}
(function() {
    // Check if we're on the main page/index
    var isMainPage = window.location.pathname === '/' || window.location.pathname === '/index';

    if (isMainPage) {
        // Simple but effective approach

        // Push a state immediately
        history.pushState(null, null, location.href);

        // Listen for back button
        window.onpopstate = function() {
            // Push state again to prevent going back
            history.pushState(null, null, location.href);
        };

        // Alternative method using onbeforeunload
        window.onbeforeunload = function() {
            return "Are you sure you want to leave this page?";
        };

        // Block using addEventListener as backup
        window.addEventListener('popstate', function(e) {
            // Prevent the back action
            history.pushState(null, null, location.href);
            e.preventDefault();
            return false;
        }, false);

        // Additional protection with hashchange
        window.addEventListener('hashchange', function(e) {
            e.preventDefault();
            location.hash = '';
            return false;
        });

    } else {
        // For other authenticated pages: Use logout behavior

        // Listen for popstate event (back/forward button clicks)
        window.addEventListener('popstate', function(event) {
            // User clicked back or forward button - terminate session
            terminateSession('You have been logged out for security reasons. Browser navigation detected.');
        });

        // Listen for page show event (when page becomes visible)
        window.addEventListener('pageshow', function(event) {
            if (event.persisted) {
                terminateSession('You have been logged out for security reasons. Browser navigation detected.');
            }
        });

        // Function to terminate session completely
        function terminateSession(message) {
            sessionStorage.clear();
            localStorage.clear();

            alert(message);

            fetch('/logout', {
                method: 'GET',
                credentials: 'same-origin'
            }).then(function() {
                window.location.replace('/');
            }).catch(function() {
                window.location.replace('/');
            });
        }
    }
})();
{% endif %}
</script>


</body>
</html>
