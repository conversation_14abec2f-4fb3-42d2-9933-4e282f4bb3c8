{% extends "header_login.html" %}

{% block content %}
<!DOCTYPE html>
<html>
<head>
    <title>Renew <PERSON></title>
    <style>
        /* Reset and base styles */
        body, h1, h2, p, div {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        /* Dark mode base styles */
        body {
    background: linear-gradient(135deg, #080b0e 0%, #1c2437 100%, #0f1419 100%) fixed;
    background-size: cover;
    color: #ffffff;
    min-height: 100vh;
}

        /* Input field styling for dark mode */
        input[type="text"],
        input[type="text"]:focus,
        input[type="text"]:active,
        input[type="text"]:hover,
        input[type="password"],
        input[type="password"]:focus,
        input[type="email"],
        input[type="email"]:focus,
        textarea,
        textarea:focus,
        select,
        select:focus {
            background-color: #333333 !important;
            color: #ffffff !important;
            border: 1px solid #444444;
            outline-color: #1e3c72;
        }

        /* Override browser autofill styles which often use white backgrounds */
        input:-webkit-autofill,
        input:-webkit-autofill:hover,
        input:-webkit-autofill:focus,
        input:-webkit-autofill:active {
            -webkit-box-shadow: 0 0 0 30px #333333 inset !important;
            -webkit-text-fill-color: #ffffff !important;
            transition: background-color 5000s ease-in-out 0s;
        }

        /* Main container */
        .container {
            max-width: 1200px;
            margin: 40px auto;
            padding: 0 20px;
        }

        /* Content wrapper */
        .content {
            max-width: 500px;
            margin: 0 auto;
            text-align: center;
        }

        .content p {
            color: #b3b3b3;
            font-size: 14px;
            margin-bottom: 30px;
            text-align: center;

        }

        /* Page title */
        .page-title {
            font-size: 48px;
            font-weight: 900;
            color: #ffffff;
            margin-bottom: 15px;
            text-align: center;
            letter-spacing: -0.04em;
        }

        /* Username display */
        .username-display {
            color: #4aa4c0;
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 10px;
            text-align: center;
        }

        .username-label {
            color: #b3b3b3;
            font-size: 14px;
            font-weight: 400;
            margin-bottom: 5px;
        }

        /* Subtitle */
        .subtitle {
            color: #b3b3b3;
            font-size: 16px;
            margin-bottom: 40px;
            text-align: center;
            line-height: 1.4;
        }

        /* Form styling */
        .renew-form {
            background-color: #282828;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 25px;
        }

        .renew-form label {
            display: block;
            font-weight: 500;
            margin-bottom: 8px;
            color: #ffffff;
            text-align: left;
            font-size: 16px;
        }

        .renew-form input[type="text"] {
            width: 100%;
            padding: 14px;
            background-color: #333333;
            border: 1px solid #444444;
            border-radius: 8px;
            margin-bottom: 25px;
            font-size: 16px;
            color: #ffffff;
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
            box-sizing: border-box;
        }

        .renew-form input[type="text"]:focus {
            border-color: #4aa4c0;
            box-shadow: 0 0 0 2px rgba(74, 164, 192, 0.2);
            outline: none;
        }

        .renew-form button {
            width: 100%;
            padding: 14px;
            background-color: rgb(74, 164, 192);
            color: #000000;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .renew-form button:hover {
            background-color: #5dcef0;
            transform: scale(1.02);
        }

        /* Buy token button */
        .buy-token-btn {
            display: inline-block;
            padding: 12px 30px;
            background-color: #404040;
            color: #ffffff;
            text-decoration: none;
            border-radius: 25px;
            font-size: 14px;
            font-weight: 600;
            border: 1px solid #666666;
            transition: all 0.3s ease;
        }

        .buy-token-btn:hover {
            background-color: #505050;
            transform: scale(1.05);
            color: #ffffff;
        }

        /* Error message styling */
        .error-message {
            background-color: #E91429;
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 14px;
            text-align: center;
        }

        /* Back link */
        .back-link {
            margin-top: 30px;
        }

        .back-link a {
            color: #b3b3b3;
            text-decoration: none;
            font-size: 14px;
            transition: color 0.3s ease;
        }

        .back-link a:hover {
            color: #4aa4c0;
        }

        /* Responsive design */
        @media (max-width: 600px) {
            .container {
                padding: 20px 15px;
                margin: 20px auto;
            }

            .page-title {
                font-size: 32px;
            }

            .renew-form {
                padding: 25px 20px;
            }

            .renew-form input[type="text"],
            .renew-form button {
                padding: 12px;
            }
        }
    </style>
</head>
<body>
<div class="container">
    <div class="content">
        <h1 class="page-title">Renew Token</h1>

        {% if username %}
        <div class="username-label">Renewing token for:</div>
        <div class="username-display">{{ username }}</div>
        {% endif %}

        <p class="subtitle">Enter your new token to extend your account access and continue using URLCHECK services.</p>

        {% if error %}
        <div class="error-message">{{ error }}</div>
        {% endif %}

        <form method="POST" class="renew-form">
            <input type="hidden" name="username" value="{{ username }}">
            <label for="token">New Token:</label>
            <input type="text" name="token" id="token" placeholder="Enter your new token" required>
            <button type="submit">RENEW TOKEN</button>
        </form>

        <a href="https://shopee.com.my/" class="buy-token-btn" target="_blank">Buy Token Here</a>

        <div class="back-link">
            <a href="{{ url_for('signin_page') }}">← Back to Login</a>
        </div>
    </div>

</div>
</body>
</html>

{% endblock %}