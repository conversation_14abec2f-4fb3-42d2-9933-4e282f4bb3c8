<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>URLCHECK</title>
  <link rel="icon" href="{{ url_for('static', filename='admin_favicon.ico') }}" type="image/x-icon">
    <style>
/* Base Styles */

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f4f4f9;
    color: #333;
    line-height: 2;
}

/* Header */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(90deg, #000000, #000000);
    padding: 15px 20px;
    color: white;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    position: relative;
}

.header .logo {
    font-size: 26px;
    font-weight: bold;
}

.header .logo a {
    color: #ffffff;
    text-decoration: none;
    transition: color 0.3s ease;
}

.header .logo a:hover {
    color: #ffffff;
}

/* Navigation */
.nav-links {
    display: flex;
    gap: 15px;
    align-items: center;
}

.nav-links a {
    color: #ffffff;
    text-decoration: none;
    font-size: 16px;
    padding: 8px 14px;

    transition: background-color 0.3s ease, color 0.3s ease;
}

.nav-links a:hover {
    background-color: #000000;
    color: #d19af3;
}

/* Hamburger Icon */
.menu-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 5px;
}

.menu-toggle div {
    width: 25px;
    height: 3px;
    background-color: white;
    transition: all 0.3s ease;
    border-radius: 2px;
}

/* Hamburger animation */
.menu-toggle.active div:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
}

.menu-toggle.active div:nth-child(2) {
    opacity: 0;
}

.menu-toggle.active div:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
}

/* Mobile Dropdown */
@media (max-width: 768px) {
    .nav-links {
        display: none;
        flex-direction: column;
        background-color: #000000;
        position: fixed;
        top: 65px;
        right: 0;
        width: 250px;
        z-index: 1000;
        border-bottom-left-radius: 15px;
        border-top-left-radius: 15px;
        box-shadow: -4px 4px 12px rgba(0, 0, 0, 0.3);
        transform: translateX(100%);
        transition: transform 0.3s ease-in-out;
    }

    .nav-links.active {
        display: flex;
        transform: translateX(0);
    }

    /* Hide profile dropdown in mobile - we'll show items directly */
    .nav-links .profile-dropdown {
        display: none !important;
    }

    /* Show mobile username display */
    .nav-links .mobile-username {
        display: flex !important;
    }

    /* Mobile menu items styling */
    .nav-links a {
        padding: 16px 20px;
        border-bottom: 1px solid #333333;
        font-size: 16px;
        transition: background-color 0.2s ease;
        white-space: nowrap;
        display: flex;
        align-items: center;
    }

    .nav-links a:hover {
        background-color: #333333;
        color: #d19af3;
    }

    .nav-links a:last-child {
        border-bottom: none;
    }

    /* Mobile username display */
    .mobile-username {
        padding: 16px 20px;
        border-bottom: 1px solid #333333;
        font-size: 16px;
        color: #d19af3;
        display: flex;
        align-items: center;
    }

    .mobile-username img {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        margin-right: 10px;
    }

    .menu-toggle {
        display: flex;
        z-index: 1001;
    }
}

/* Main Content */


/* Extra small screens */
@media (max-width: 480px) {
    .header {
        padding: 12px 15px;
    }

    .header .logo {
        font-size: 20px;
    }

    .nav-links {
        width: 100%;
        top: 57px;
        border-radius: 0;
        right: 0;
        transform: translateX(100%);
    }

    .nav-links.active {
        transform: translateX(0);
    }

    .content h1 {
        font-size: 24px;
    }

    .content p {
        font-size: 14px;
    }

    .menu-toggle div {
        width: 22px;
        height: 2px;
    }
}

/* Tablet and medium screens */
@media (max-width: 1024px) and (min-width: 769px) {
    .nav-links {
        gap: 12px;
    }

    .nav-links a {
        font-size: 15px;
        padding: 6px 12px;
    }
}

.profile-dropdown {
    position: relative;
    display: inline-block;
}

.profile-button {
    background: none;
    border: none;
    color: white;
    font-size: 16px;
    cursor: pointer;
    display: flex;
    align-items: center;
}

.mini-profile-pic {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    object-fit: cover;
    margin-right: 8px;
}

#dropdown-content {
    display: none;
    position: absolute;
    right: 0px;
    background-color:#000000; /* black */
    min-width: 220px;
    box-shadow: 0px 8px 16px rgba(0,0,0,0.2);
    border-radius: 10px;
    overflow: hidden;
    z-index: 1;
    text-align: center;
    padding: 15px;
}

.large-profile-pic {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    margin-bottom: 10px;
}

.signout-button {
    margin-top: 15px;
    padding: 8px 16px;
    background: white;
    border: 1px solid #ccc;
    border-radius: 8px;
    cursor: pointer;
}

.signout-button:hover {
    background-color: #f2f2f2;
}


  </style>
  <script>
    function toggleDropdown() {
        var dropdown = document.getElementById("dropdown-content");
        dropdown.style.display = (dropdown.style.display === "block") ? "none" : "block";
    }

    // Hide dropdown if clicked outside
    window.onclick = function(event) {
      if (!event.target.matches('.profile-button') && !event.target.closest('.profile-dropdown')) {
        var dropdowns = document.getElementsByClassName("dropdown-content");
        for (var i = 0; i < dropdowns.length; i++) {
          var openDropdown = dropdowns[i];
          if (openDropdown.style.display === "block") {
            openDropdown.style.display = "none";
          }
        }
      }
    }
    </script>
</head>
<body>

<div class="header">
    <div class="logo">
        <a href="/dashboard_admin">URLCHECK</a>
    </div>

    <!-- Hamburger Icon -->
    <div class="menu-toggle" onclick="toggleMenu()">
        <div></div>
        <div></div>
        <div></div>
    </div>

    <!-- Navigation Links -->
    <div class="nav-links" id="navLinks">
        <a href="/dashboard_admin">Dashboard</a>
        <a href="/admin_archiveurl">ArchiveURL</a>
        <a href="/update_trainingmodel">Training Model</a>
        <a href="/generate_token">Token</a>
        <a href="/manage_user">Manage User</a>
        <a href="/manage_contact_us">Manage Contact Us</a>
        <a href="/logout">Logout</a>

        {% if 'username' in session %}
            <!-- Mobile username display (hidden on desktop) -->
            <div class="mobile-username" style="display: none;">
                {% if profile_picture %}
                <img src="data:image/jpeg;base64,{{ profile_picture | b64encode }}" alt="Profile Picture">
                {% else %}
                <img src="{{ url_for('static', filename='default_profile.png') }}" alt="Default Profile">
                {% endif %}
                {{ username }}
            </div>

            <!-- Desktop profile dropdown (hidden on mobile) -->
            <div class="profile-dropdown">
                <button class="profile-button" onclick="toggleDropdown()">
                    {% if profile_picture %}
                    <img src="data:image/jpeg;base64,{{ profile_picture | b64encode }}" alt="Profile Picture" class="mini-profile-pic">
                    {% else %}
                    <img src="{{ url_for('static', filename='default_profile.png') }}" alt="Default Profile" class="mini-profile-pic">
                    {% endif %}
                    {{ username }}
                </button>

                <div id="dropdown-content" class="dropdown-content">
                    <div class="profile-info">
                        {% if profile_picture %}
                        <img src="data:image/jpeg;base64,{{ profile_picture | b64encode }}" alt="Profile Picture" class="large-profile-pic">
                        {% else %}
                        <img src="{{ url_for('static', filename='default_profile.png') }}" alt="Default Profile" class="large-profile-pic">
                        {% endif %}
                    </div>
                    <a href="/admin_profile">Edit Profile</a>
                    <a href="/logout">Logout</a>
                </div>
            </div>
        {% else %}
            <a href="/login">Login</a>
            <a href="/register">Register</a>
        {% endif %}
    </div>

</div>

<div class="content">
    {% block content %}
    <div>
        <h1>{{ page_title }}</h1>
        <p>{{ content }}</p>
    </div>
    {% endblock %}
</div>

<script>
function toggleMenu() {
    var navLinks = document.getElementById('navLinks');
    var menuToggle = document.querySelector('.menu-toggle');

    navLinks.classList.toggle('active');
    menuToggle.classList.toggle('active');

    // Prevent body scroll when menu is open
    if (navLinks.classList.contains('active')) {
        document.body.style.overflow = 'hidden';
    } else {
        document.body.style.overflow = '';
    }
}

// Close mobile menu when clicking outside
document.addEventListener('click', function(event) {
    var navLinks = document.getElementById('navLinks');
    var menuToggle = document.querySelector('.menu-toggle');
    var header = document.querySelector('.header');

    if (!header.contains(event.target) && navLinks.classList.contains('active')) {
        navLinks.classList.remove('active');
        menuToggle.classList.remove('active');
        document.body.style.overflow = '';
    }
});

// Close mobile menu when window is resized to desktop
window.addEventListener('resize', function() {
    var navLinks = document.getElementById('navLinks');
    var menuToggle = document.querySelector('.menu-toggle');

    if (window.innerWidth > 768 && navLinks.classList.contains('active')) {
        navLinks.classList.remove('active');
        menuToggle.classList.remove('active');
        document.body.style.overflow = '';
    }
});

// Auto-logout on browser back button for authenticated users
{% if 'email' in session or 'username' in session %}
(function() {
    // Track navigation state
    let isNavigatingAway = false;
    let currentUrl = window.location.href;

    // Set initial state
    if (!sessionStorage.getItem('userLoggedIn')) {
        sessionStorage.setItem('userLoggedIn', 'true');
        sessionStorage.setItem('lastVisitedPage', currentUrl);
    }

    // Listen for popstate event (back/forward button)
    window.addEventListener('popstate', function(event) {
        // User used back button, log them out
        logoutUser();
    });

    // Listen for page visibility changes
    document.addEventListener('visibilitychange', function() {
        if (document.visibilityState === 'hidden') {
            // User is leaving the page
            isNavigatingAway = true;
            sessionStorage.setItem('lastVisitedPage', currentUrl);
        } else if (document.visibilityState === 'visible' && isNavigatingAway) {
            // User came back to the page
            // Check if this was a back navigation by comparing URLs
            let lastPage = sessionStorage.getItem('lastVisitedPage');
            if (lastPage && lastPage !== currentUrl &&
                performance.navigation.type === 2) { // TYPE_BACK_FORWARD
                logoutUser();
            }
            isNavigatingAway = false;
        }
    });

    // Function to logout user
    function logoutUser() {
        // Clear session storage
        sessionStorage.clear();

        // Show logout message
        alert('You have been logged out for security reasons.');

        // Redirect to logout endpoint
        window.location.href = '/logout';
    }

    // Clean up when leaving the page normally
    window.addEventListener('beforeunload', function() {
        if (!isNavigatingAway) {
            sessionStorage.setItem('lastVisitedPage', currentUrl);
        }
    });

    // Handle normal navigation (clicking links)
    document.addEventListener('click', function(event) {
        if (event.target.tagName === 'A' && event.target.href) {
            isNavigatingAway = true;
            sessionStorage.setItem('lastVisitedPage', currentUrl);
        }
    });
})();
{% endif %}
</script>

</body>
</html>
