{% extends "header_loginadmin.html" %}

{% block content %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Admin Login</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@100;300;400;500;700;900&display=swap" rel="stylesheet">
    <style>
        /* Reset and base styles */
        body, h1, h2, p, div {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        /* Dark theme base styles */
        body {
            background: linear-gradient(135deg, #141719 0%, #121212 100%);
            color: #ffffff;
            min-height: 100vh;
        }

        /* Input field styling for dark mode */
        input[type="text"],
        input[type="text"]:focus,
        input[type="text"]:active,
        input[type="text"]:hover,
        input[type="password"],
        input[type="password"]:focus,
        input[type="email"],
        input[type="email"]:focus,
        textarea,
        textarea:focus,
        select,
        select:focus {
            background-color: #333333 !important;
            color: #ffffff !important;
            border: 1px solid #444444;
            outline-color: #b148f3;
        }

        /* Override browser autofill styles */
        input:-webkit-autofill,
        input:-webkit-autofill:hover,
        input:-webkit-autofill:focus,
        input:-webkit-autofill:active {
            -webkit-box-shadow: 0 0 0 30px #333333 inset !important;
            -webkit-text-fill-color: #ffffff !important;
            transition: background-color 5000s ease-in-out 0s;
        }


        /* Main container */
        .container {
            max-width: 1200px;
            margin: 40px auto;
            padding: 0 0px;
        }

        /* Page title */
        .page-title {
            font-family: 'Poppins', sans-serif;
            font-size: 48px;
            font-weight: 900;
            color: #ffffff;
            margin-bottom: 30px;
            text-align: center;
            letter-spacing: -0.04em;
        }

        .content p {
            color: #b3b3b3;
            font-size: 14px;
            margin-bottom: 30px;
            text-align: center;
        }


        label {
            display: block;
            font-weight: 500;
            margin-bottom: 5px;
            color: #b3b3b3;
            font-size: 16px;
        }


        .modal {
            display: none;
            position: fixed;
            z-index: 10;
            left: 0; top: 0;
            width: 100%; height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: #282828;
            margin: 15% auto;
            padding: 20px;
            width: 300px;
            text-align: center;
            border-radius: 8px;
            color: #ffffff;
        }

        .modal button {
            margin: 5px;
        }




        /* Login Form Styles */
        .login-form {
            display: flex;
            flex-direction: column;
            width: 30%;
            margin: auto;
            position: center;
        }

        .login-form label {
            font-weight: 500;
            margin-bottom: 5px;
            color: #b3b3b3;
        }

        .login-form input[type="text"],
        .login-form input[type="email"],
        .login-form input[type="password"] {
            padding: 10px 12px;
            background-color: #282828;
            border: 1px solid #333;
            border-radius: 30px;
            margin-bottom: 18px;
            font-size: 14px;
            color: #ffffff;
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
        }

        .login-form input:focus {
            border-color: #b148f3;
            box-shadow: 0 0 0 2px rgba(177, 72, 243, 0.2);
            outline: none;
        }

        .login-form button {
            margin-top: 0px;
            padding: 12px;
            background-color: #b148f3;
            color: #ffffff;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.3s ease, transform 0.2s ease;
        }

        .login-form button:hover {
            background-color: #9a3dd9;
            transform: scale(1.02);
        }

        .register-link {
            margin-top: 20px;
            text-align: center;
        }

        .register-link a {
            text-decoration: none;
            color: #b3b3b3;
            font-weight: 500;
            transition: color 0.3s ease;
            font-size: 14px;
        }

        .register-link a:hover {
            color: #b148f3;
        }

        .forgot-link {
            margin-top: -19px;
            margin-bottom: 18px;
            text-align: right;
        }

        .forgot-link a {
            text-decoration: none;
            color: #b3b3b3;
            font-size: 12px;
            transition: color 0.3s ease;
        }

        .forgot-link a:hover {
            color: #b148f3;
        }

        /* Flash messaes container */
        .flash-messages {
            position: fixed;
            top: 85px;
            right: -2px;
            z-index: 9999;
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .flash-messages li {
            background-color: #ff0019a8; /* Default red for errors */
            color: white;
            padding: 15px 25px;
            margin-top: 10px;
            border-radius: 4px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.4);
            font-size: 14px;
            opacity: 0;
            transform: translateX(100%);
            animation: slideInOut 5s forwards;
            cursor: pointer;
            min-width: 250px;
        }

        /* Success message styling */
        .flash-messages li.success {
            background-color: #00b2f880; /* Spotify blue for success */
        }

        /* Error message styling */
        .flash-messages li.error {
            background-color: #ff0019a8; /* Red for errors */
        }

        /* Warning message styling */
        .flash-messages li.warning {
            background-color: #ff0019a8; /* Red for warnings */
        }

        /* Animation for slide in from right and fade out */
        @keyframes slideInOut {
            0% {
                opacity: 0;
                transform: translateX(100%);
            }
            10% {
                opacity: 1;
                transform: translateX(0);
            }
            90% {
                opacity: 1;
                transform: translateX(0);
            }
            100% {
                opacity: 0;
                transform: translateX(100%);
            }
        }

        @keyframes spin {
            0% { transform: rotate(0deg);}
            100% { transform: rotate(360deg);}
        }

        /* Responsive - make it more mobile friendly */
        @media (max-width: 600px) {
            .content {
                padding: 20px 15px;
                margin-top: 30px;
            }

            .content h1 {
                font-size: 24px;
            }

            .login-form button {
                padding: 12px;
                font-size: 15px;
            }

            .flash-messages li {
                min-width: 200px;
                padding: 12px 20px;
                font-size: 13px;
            }
        }
    </style>
    <script>
        // JavaScript function to show alert if a message exists
        function showAlert(message) {
            if (message) {
                alert(message);
            }
        }
        // Remove flash message on click
        document.addEventListener('DOMContentLoaded', function() {
            const flashMessages = document.querySelectorAll('.flash-messages li');
            flashMessages.forEach(function(msg) {
                msg.addEventListener('click', function() {
                    msg.style.animation = 'slideOut 0.5s forwards';
                    setTimeout(() => {
                        msg.remove();
                    }, 500);
                });
            });
        });
    </script>
</head>
<body>
<div class="container">
    <div class="content">
        <h1 class="page-title">Admin Login</h1>

        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
              <ul class="flash-messages" id="flash-messages">
                {% for category, message in messages %}
                  <li class="{{ category }}">{{ message }}</li>
                {% endfor %}
              </ul>
            {% endif %}
        {% endwith %}

        <form method="POST" action="/login_admin" class="login-form">
            <label for="username">Username</label>
            <input type="text" id="username" name="username" required>

            <label for="password">Password</label>
            <input type="password" id="password" name="password" required>

            <button type="submit">LOG IN</button>
        </form>
    </div>

    <p2 style="text-align:center; font-family: Arial, serif; font-size: 11px; color: #535151; position: fixed; bottom: 0px;  margin-bottom: 15px; width: 100%; left: 0px;">© URLCHECK 2025.All rights reserved</p2>

    <!-- Pop-up alert -->
    <script>
        // Fetch any error message passed from Flask and display it
        showAlert('{{ message }}');
    </script>
</div>
</body>
</html>
{% endblock %}