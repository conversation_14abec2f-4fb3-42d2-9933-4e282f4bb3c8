{% if is_admin %}
    {% extends "header_register.html" %}
{% else %}
    {% extends "header.html" %}
{% endif %}

{% block content %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>URLCHECK - Reset Password</title>
    <style>
        /* Reset and base styles */
        body, h1, h2, p, div {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        /* Dark mode base styles */
        body {
    background: linear-gradient(135deg, #080b0e 0%, #1c2437 100%, #0f1419 100%) fixed;
    background-size: cover;
    color: #ffffff;
    min-height: 100vh;
}

        /* Input field styling for dark mode */
        input[type="text"],
        input[type="text"]:focus,
        input[type="text"]:active,
        input[type="text"]:hover,
        input[type="password"],
        input[type="password"]:focus,
        input[type="email"],
        input[type="email"]:focus,
        textarea,
        textarea:focus,
        select,
        select:focus {
            background-color: #333333 !important;
            color: #ffffff !important;
            border: 1px solid #5dcef0;
            outline-color: #1e3c72;
        }

        /* Override browser autofill styles */
        input:-webkit-autofill,
        input:-webkit-autofill:hover,
        input:-webkit-autofill:focus,
        input:-webkit-autofill:active {
            -webkit-box-shadow: 0 0 0 30px #333333 inset !important;
            -webkit-text-fill-color: #ffffff !important;
            transition: background-color 5000s ease-in-out 0s;
        }

        /* Main container */
        .container {
            max-width: 1200px;
            margin: 40px auto;
            padding: 0 0px;
            margin-bottom: 170px;
        }

        /* Page title */
        .page-title {
            font-family: 'Roboto', sans-serif;
            font-size: 48px;
            font-weight: 900;
            color: #ffffff;
            margin-top: 60px;
            margin-bottom: 30px;
            text-align: center;
            letter-spacing: -0.04em;
            line-height: 1.2;
        }

         .content p {
            color: #b3b3b3;
            font-size: 14px;
            margin-bottom: 30px;
            text-align: center;



        }

         .content p {
            color: #b3b3b3;
            font-size: 14px;
            margin-bottom: 30px;
            text-align: center;
        }

        form {
            display: flex;
            flex-direction: column;
            width: 33%;
            margin: auto;
        }

        label {
            font-weight: 600;
            margin-bottom: 8px;
            color: #b3b3b3;
            font-size: 12px;
        }

        input[type="password"] {
            padding: 10px 12px;
            border: 1px solid #333;
            background-color: #282828;
            border-radius: 30px;
            margin-bottom: 20px;
            font-size: 14px;
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
        }

        input:focus {
            border-color: #5dcef0;
            box-shadow: 0 0 0 2px rgba(29, 185, 84, 0.15);
            outline: none;
        }

        .password-section {
            display: flex;
            flex-direction: column;
        }

        .password-input-wrapper {
            position: relative;
            display: block;
            width: 100%;
        }

        /* Tooltip styles */
        .tooltip {
            position: relative;
            display: block;
            width: 100%;
        }

        .tooltip input {
            width: 100%;
            box-sizing: border-box;
        }

        .tooltip .tooltiptext {
            visibility: hidden;
            width: 280px;
            background-color: #333333;
            color: #ffffff;
            text-align: left;
            border-radius: 6px;
            padding: 10px;
            position: absolute;
            z-index: 1;
            bottom: 100%;
            left: 60px;
            margin-bottom: 10px;
            opacity: 0;
            transition: opacity 0.3s;
            border: 1px solid #5dcef0;
            font-size: 12px;
            line-height: 1.4;
        }

        .tooltip .tooltiptext::after {
            content: "";
            position: absolute;
            top: 100%;
            left: 20px;
            margin-left: -5px;
            border-width: 5px;
            border-style: solid;
            border-color: #5dcef0 transparent transparent transparent;
        }

        .tooltip:hover .tooltiptext {
            visibility: visible;
            opacity: 1;
        }

        .tooltip-criteria {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .tooltip-criteria li {
            margin: 5px 0;
            color: #cb1023;
            font-size: 11px;
        }

        .tooltip-criteria li.valid {
            color: #1DB954;
        }

        .tooltip-criteria li::before {
            content: "✗ ";
            margin-right: 5px;
        }

        .tooltip-criteria li.valid::before {
            content: "✓ ";
        }

        button {
            padding: 12px;
            background-color: #4aa4c0;
            color: rgb(0, 0, 0);
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.3s ease, transform 0.2s ease;
            margin-top: 10px;
        }

        button:disabled {
            background-color: #333333;
            cursor: not-allowed;
        }

        button:hover:enabled {
            background-color: #5dcef0;
            transform: scale(1.02);
        }

        .login-link {
            text-align: center;
            margin-top: 20px;
            font-size: 14px;
        }

        .login-link a {
            color: #b3b3b3;
            text-decoration: none;
            font-weight: 600;
            transition: color 0.3s ease;
        }

        .login-link a:hover {
            color: #4aa4c0;
        }

        /* Error message styling */
        .error {
            color: #ffffff;
            background-color: rgba(233, 20, 41, 0.2);
            padding: 10px;
            border: 1px solid #ff0019a8;
            border-radius: 6px;
            margin-bottom: 15px;
            font-size: 14px;
        }

        /* Flash messages */
        .flash-messages {
            position: fixed;
            top: 24px;
            right: 24px;
            z-index: 9999;
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .flash-message {
            padding: 16px 20px;
            margin-bottom: 12px;
            border-radius: 4px;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);
            font-size: 14px;
            font-weight: 400;
            opacity: 0;
            transform: translateX(100%);
            animation: slideInOut 5s forwards;
            cursor: pointer;
            min-width: 300px;
            border-left: 4px solid #ffffff;
        }

        .flash-message.success {
            background-color: #5dcef0;
            color: #000000;
            border-left-color: #000000;
        }

        .flash-message.error {
            background-color: #e22134;
            color: #ffffff;
            border-left-color: #ffffff;
        }

        .flash-message.info {
            background-color: #1e3a8a;
            color: #ffffff;
            border-left-color: #ffffff;
        }

        .flash-message.warning {
            background-color: #f59e0b;
            color: #000000;
            border-left-color: #000000;
        }

        @keyframes slideInOut {
            0% {
                opacity: 0;
                transform: translateX(100%);
            }
            10% {
                opacity: 1;
                transform: translateX(0);
            }
            90% {
                opacity: 1;
                transform: translateX(0);
            }
            100% {
                opacity: 0;
                transform: translateX(100%);
            }
        }

        /* Password strength indicator */
        .password-strength {
            margin-top: 8px;
            font-size: 12px;
        }

        .strength-bar {
            height: 4px;
            background-color: #282828;
            border-radius: 2px;
            margin-top: 4px;
            overflow: hidden;
        }

        .strength-fill {
            height: 100%;
            transition: all 0.3s ease;
            border-radius: 2px;
        }

        .strength-weak { background-color: #e22134; width: 25%; }
        .strength-fair { background-color: #f59e0b; width: 50%; }
        .strength-good { background-color: #10b981; width: 75%; }
        .strength-strong { background-color: #5dcef0; width: 100%; }

        /* Standardized Mobile Responsive Design */
        @media (max-width: 768px) {
            .container {
                margin: 20px auto;
                padding: 0 20px;
                margin-bottom: 120px;
            }

            .page-title {
                font-size: 32px;
                margin-bottom: 30px;
                margin-top: 50px;
            }

            .form-container {
                padding: 25px;
            }

            .flash-messages {
                right: 20px;
                top: 20px;
            }

            .flash-message {
                min-width: 300px;
                padding: 15px 18px;
                font-size: 14px;
            }
        }

        @media (max-width: 600px) {
            .container {
                margin: 15px auto;
                padding: 0 15px;
                margin-bottom: 100px;
            }

            .page-title {
                font-size: 28px;
                margin-bottom: 25px;
            }

            .form-container {
                padding: 24px;
            }

            .flash-messages {
                right: 15px;
                top: 15px;
            }

            .flash-message {
                min-width: 280px;
                padding: 14px 16px;
                font-size: 13px;
            }
        }

        @media (max-width: 480px) {
            .container {
                margin: 10px auto;
                padding: 0 10px;
                margin-bottom: 80px;
            }

            .page-title {
                font-size: 24px;
                margin-bottom: 20px;
            }

            .form-container {
                padding: 20px;
            }

            .flash-messages {
                right: 10px;
                top: 10px;
            }

            .flash-message {
                min-width: 260px;
                padding: 12px 14px;
                font-size: 12px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="page-title">Reset<br>Password</h1>

        {% with messages = get_flashed_messages(category_filter=["error"]) %}
          {% if messages %}
            <div class="error">
              {% for message in messages %}
                <p>{{ message }}</p>
              {% endfor %}
            </div>
          {% endif %}
        {% endwith %}

        <form method="POST" action="{{ url_for('forgotpassword') }}" onsubmit="return validateForm()">
            <div class="password-section">

                <label for="new_password">New Password:</label>
                <div class="tooltip">
                    <input type="password" name="new_password" id="new_password" oninput="validatePassword()" required>
                    <span class="tooltiptext">
                        <strong>Password Requirements:</strong>
                        <ul class="tooltip-criteria">
                            <li id="uppercase">At least 1 uppercase letter (A-Z)</li>
                            <li id="lowercase">At least 1 lowercase letter (a-z)</li>
                            <li id="digit">At least 1 digit (0-9)</li>
                            <li id="symbol">At least 1 symbol (!@#$%^&*)</li>
                            <li id="length">At least 12 characters</li>
                            <li id="match">Passwords match</li>
                        </ul>
                    </span>
                </div>

                <label for="confirm_password">Confirm New Password:</label>
                <div class="tooltip">
                    <input type="password" name="confirm_password" id="confirm_password" oninput="validatePassword()" required>
                    <span class="tooltiptext">
                        <strong>Confirm Password:</strong>
                        <ul class="tooltip-criteria">
                            <li id="match-confirm">Passwords must match</li>
                            <li id="not-empty-confirm">Field cannot be empty</li>
                        </ul>
                    </span>
                </div>
            </div>

            <button type="submit" id="submitBtn">Update Password</button>
        </form>

        <div class="login-link">
            <a href="{{ url_for('signin_page') }}">Back to Login</a>
        </div>
    </div>

    <script>
        function validatePassword() {
            const password = document.getElementById("new_password").value;
            const confirmPassword = document.getElementById("confirm_password").value;

            // Regular Expressions for New Password
            const hasUpper = /[A-Z]/.test(password);
            const hasLower = /[a-z]/.test(password);
            const hasDigit = /[0-9]/.test(password);
            const hasSymbol = /[!@#$%^&*]/.test(password);
            const isLongEnough = password.length >= 12;
            const passwordsMatch = password === confirmPassword && password.length > 0;

            // Toggle valid/invalid classes for New Password tooltip
            document.getElementById("uppercase").classList.toggle("valid", hasUpper);
            document.getElementById("lowercase").classList.toggle("valid", hasLower);
            document.getElementById("digit").classList.toggle("valid", hasDigit);
            document.getElementById("symbol").classList.toggle("valid", hasSymbol);
            document.getElementById("length").classList.toggle("valid", isLongEnough);
            document.getElementById("match").classList.toggle("valid", passwordsMatch);

            // Toggle valid/invalid classes for Confirm Password tooltip
            const confirmNotEmpty = confirmPassword.length > 0;
            document.getElementById("match-confirm").classList.toggle("valid", passwordsMatch);
            document.getElementById("not-empty-confirm").classList.toggle("valid", confirmNotEmpty);
        }

        function validateForm() {
            const password = document.getElementById("new_password").value;
            const confirmPassword = document.getElementById("confirm_password").value;

            if (password !== confirmPassword) {
                alert("Passwords do not match!");
                return false;
            }
            return true;
        }

        const form = document.querySelector('form');
        const submitBtn = document.getElementById('submitBtn');


    </script>

    <p2 style="text-align:center; font-family: Arial, serif; font-size: 11px; color: #535151; position: fixed; bottom: 0px;  margin-bottom: 15px; width: 100%; left: 0px;">© URLCHECK 2025.All rights reserved</p2>
</body>
</html>

{% endblock %}