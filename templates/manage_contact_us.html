{% extends "header_admin.html" %}
{% block content %}

<!DOCTYPE html>
<html>
<head>
    <title>Manage Contact Messages</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@100;300;400;500;700;900&display=swap" rel="stylesheet">
    <style>
          /* Reset and base styles */
        body, h1, h2, p, div, table, tr, th, td {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        /* Dark theme base styles */
        body {
            background: linear-gradient(135deg, #141719 0%, #121212 100%);
            color: #ffffff;
            min-height: 100vh;
        }

        /* Container */
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 40px 20px;
            min-height: calc(100vh - 200px);
        }

        /* Page title */
        .page-title {
            font-family: 'Roboto', sans-serif;
            font-size: 48px;
            font-weight: 900;
            color: #ffffff;
            margin-bottom: 48px;
            text-align: center;
            letter-spacing: -0.04em;
        }

        /* Main content container */
        .content {
            width: 100%;
            background-color: transparent;
            margin: 0 auto;
            border-radius: 12px;
            padding: 20px 0;
            box-sizing: border-box;
        }

        /* Messages Grid Layout - 2 cards per row like other admin pages */
        .messages-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 30px;
            margin-top: 30px;
            padding: 0 20px;
            max-width: 1400px;
            margin-left: auto;
            margin-right: auto;
        }

        /* Message Card Styling - standardized with manage_user */
        .message-card {
            background: linear-gradient(145deg, #2a2a2a 0%, #1e1e1e 100%);
            border: 1px solid #404040;
            border-radius: 16px;
            padding: 25px;
            text-align: left;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            cursor: pointer;
            position: relative;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.4);
            overflow: hidden;
        }

        .message-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(177, 72, 243, 0.1), transparent);
            transition: left 0.5s;
        }

        .message-card:hover::before {
            left: 100%;
        }

        .message-card:hover {
            transform: translateY(-8px) scale(1.02);
            border-color: #b148f3;
            box-shadow: 0 15px 30px rgba(177, 72, 243, 0.3);
        }

        /* Message Header - without avatar */
        .message-header {
            margin-bottom: 15px;
        }

        .message-info h3 {
            margin: 0;
            font-size: 20px;
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 5px;
        }

        .message-info .email {
            font-size: 14px;
            color: #b3b3b3;
            margin-bottom: 10px;
        }

        .message-subject {
            font-size: 16px;
            font-weight: 500;
            color: #b148f3;
            margin-bottom: 12px;
            line-height: 1.4;
        }

        .message-preview {
            font-size: 14px;
            color: #cccccc;
            line-height: 1.5;
            margin-bottom: 15px;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .message-date {
            font-size: 12px;
            color: #888;
            text-align: right;
        }

        .no-messages {
            text-align: center;
            color: #b0b0b0;
            font-size: 18px;
            margin-top: 50px;
        }

        /* Modal styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background: linear-gradient(145deg, #2a2a2a 0%, #1e1e1e 100%);
            margin: 11% auto;
            padding: 25px;
            border: 1px solid #404040;
            border-radius: 16px;
            width: 85%;
            max-width: 600px;
            position: relative;
            bottom: 70px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.6);
            animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .close {
            color: #b3b3b3;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            transition: color 0.3s ease;
        }

        .close:hover {
            color: #b148f3;
        }

        .modal-header {
            text-align: center;
            margin-bottom: 25px;
        }

        .modal-header h2 {
            color: #ffffff;
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .modal-info {
            margin-bottom: 20px;
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 12px;
            padding: 10px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
        }

        .info-label {
            color: #b3b3b3;
            font-weight: 500;
            min-width: 120px;
        }

        .info-value {
            color: #ffffff;
            font-weight: 600;
            flex: 1;
            text-align: right;
            word-break: break-word;
        }

        .modal-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            min-width: 100px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #b148f3 0%, #9a3dd9 100%);
            color: white;
            border: 1px solid #b148f3;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #9a3dd9 0%, #8a2be2 100%);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(177, 72, 243, 0.4);
        }

        .btn-secondary {
            background: #333333;
            color: #ffffff;
            border: 1px solid #444444;
        }

        .btn-secondary:hover {
            background: #444444;
            border-color: #555555;
            transform: translateY(-2px);
        }

        /* Flash messages */
        .flash-messages {
            position: fixed;
            top: 85px;
            right: -2px;
            z-index: 9999;
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .flash-messages li {
            background-color: rgba(177, 72, 243, 0.8);
            color: white;
            padding: 15px 25px;
            margin-top: 10px;
            border-radius: 4px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.4);
            font-size: 14px;
            opacity: 0;
            transform: translateX(100%);
            animation: slideInOut 5s forwards;
            cursor: pointer;
            min-width: 250px;
        }

        @keyframes slideInOut {
            0% {
                opacity: 0;
                transform: translateX(100%);
            }
            10% {
                opacity: 1;
                transform: translateX(0);
            }
            90% {
                opacity: 1;
                transform: translateX(0);
            }
            100% {
                opacity: 0;
                transform: translateX(100%);
            }
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .page-title {
                font-size: 36px;
                margin-bottom: 30px;
            }

            .container {
                padding: 20px 15px;
            }

            .messages-grid {
                grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
                gap: 20px;
                padding: 0 10px;
            }

            .message-card {
                padding: 20px;
            }

            .modal-content {
                width: 90%;
                margin: 12% auto;
                padding: 18px;
                max-width: 350px;
            }

            .modal-actions {
                flex-direction: column;
            }

            .btn {
                width: 100%;
            }
        }

        @media (max-width: 480px) {
            .messages-grid {
                grid-template-columns: 1fr;
            }

            .page-title {
                font-size: 28px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="page-title">Contact Messages</h1>

        {% with messages = get_flashed_messages() %}
            {% if messages %}
              <ul class="flash-messages" id="flash-messages">
                {% for message in messages %}
                  <li>{{ message }}</li>
                {% endfor %}
              </ul>
            {% endif %}
        {% endwith %}

        <div class="content">
            {% if messages %}
                <div class="messages-grid">
                    {% for msg in messages %}
                    <div class="message-card" onclick="openMessageModal('{{ msg.id }}', '{{ msg.name }}', '{{ msg.email }}', '{{ msg.subject }}', '{{ msg.message|replace("'", "\\'") }}', '{{ msg.formatted_date }}')">
                        <div class="message-header">
                            <div class="message-info">
                                <h3>{{ msg.name }}</h3>
                                <div class="email">{{ msg.email }}</div>
                            </div>
                        </div>
                        <div class="message-subject">{{ msg.subject }}</div>
                        <div class="message-preview">{{ msg.message }}</div>
                        <div class="message-date">{{ msg.formatted_date }}</div>
                    </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="no-messages">
                    <p>No contact messages found.</p>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- Message Details Modal -->
    <div id="messageModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeMessageModal()">&times;</span>
            <div class="modal-header">
                <h2 id="modalName">Contact Message</h2>
            </div>
            <div class="modal-info">
                <div class="info-row">
                    <span class="info-label">Name:</span>
                    <span class="info-value" id="modalNameValue">-</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Email:</span>
                    <span class="info-value" id="modalEmail">-</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Subject:</span>
                    <span class="info-value" id="modalSubject">-</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Message:</span>
                    <span class="info-value" id="modalMessage">-</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Date:</span>
                    <span class="info-value" id="modalDate">-</span>
                </div>
            </div>
            <div class="modal-actions">
                <a href="mailto:<EMAIL>" id="contactUserBtn" class="btn btn-primary" target="_blank">Contact User</a>
                <button class="btn btn-secondary" onclick="closeMessageModal()">Close</button>
            </div>
        </div>
    </div>

 <p style="text-align:center; font-family: Arial, serif; font-size: 11px; color: #535151; bottom: 0px;  margin-bottom: 15px; width: 100%;">© URLCHECK 2025.All rights reserved</p>

    <script>
        let currentMessageId = null;

        function openMessageModal(id, name, email, subject, message, date) {
            currentMessageId = id;

            // Update modal content
            document.getElementById('modalNameValue').textContent = name;
            document.getElementById('modalEmail').textContent = email;
            document.getElementById('modalSubject').textContent = subject;
            document.getElementById('modalMessage').textContent = message;
            document.getElementById('modalDate').textContent = date;

            // Update contact user button with user's email
            const contactBtn = document.getElementById('contactUserBtn');
            contactBtn.href = `mailto:${email}?subject=Re: ${encodeURIComponent(subject)}&body=Hello ${encodeURIComponent(name)},%0A%0AThank you for contacting URLCheck.my. We have received your message regarding "${encodeURIComponent(subject)}".%0A%0A`;
            contactBtn.onclick = null; // Remove any previous onclick handlers

            // Show modal
            document.getElementById('messageModal').style.display = 'block';
        }

        function closeMessageModal() {
            document.getElementById('messageModal').style.display = 'none';
            currentMessageId = null;
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('messageModal');
            if (event.target === modal) {
                closeMessageModal();
            }
        }

        // Remove flash message on click
        document.addEventListener('DOMContentLoaded', function() {
            const flashMessages = document.querySelectorAll('.flash-messages li');
            flashMessages.forEach(function(msg) {
                msg.addEventListener('click', function() {
                    msg.style.animation = 'slideOut 0.5s forwards';
                    setTimeout(() => {
                        msg.remove();
                    }, 500);
                });
            });
        });
    </script>

</body>
</html>

{% endblock %}

