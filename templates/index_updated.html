{% if is_admin %}
    {% extends "header_admin.html" %}
{% else %}
    {% extends "header.html" %}
{% endif %}

{% block content %}


<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>URLCHECK</title>

    <style>
        /* Reset some default margins */
        body, h1, p, form {
            margin: 0;
            padding: 0;
        }

        /* Main content container */
        .content {
            width: 90%;
            max-width: 600px;
            background-color: #ffffff;
            margin: 40px auto;
            border-radius: 12px;
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
            padding: 30px 20px;
            box-sizing: border-box;
            margin-top: 50px;
        }

        .content h1 {
            color: #1e3c72;
            text-align: center;
            margin-bottom: 20px;
            font-size: 28px;
        }

        .content p {
            text-align: center;
            color: #555;
            font-size: 18px;
            margin-bottom: 20px;
        }

        form label {
            display: block;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
            font-size: 16px;
        }

        form input[type="text"] {
            width: 100%;
            padding: 12px;
            font-size: 16px;
            border: 1px solid #1e3c72;
            border-radius: 8px;
            margin-bottom: 10px;
            box-sizing: border-box;
        }

        form input[type="submit"] {
            width: 100%;
            padding: 14px;
            background-color: #1e3c72;
            color: #ffffff;
            font-size: 16px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            margin-top: 10px;
            transition: background-color 0.3s ease;
        }

        form input[type="submit"]:hover {
            background-color: #16345d;
        }

        /* Flash messages container */
        .flash-messages {
            position: fixed;
            top: 85px;
            right: 20px;
            z-index: 9999;
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .flash-messages li {
            background-color: #f44336; /* Red background */
            color: white;
            padding: 15px 25px;
            margin-top: 10px;
            border-radius: 5px;
            box-shadow: 0 2px 6px rgba(0,0,0,0.3);
            font-size: 14px;
            opacity: 0;
            transform: translateX(100%);
            animation: slideInOut 4s forwards;
            cursor: pointer;
            min-width: 250px;
        }

        /* Animation for slide in from right and fade out */
        @keyframes slideInOut {
            0% {
                opacity: 0;
                transform: translateX(100%);
            }
            10% {
                opacity: 1;
                transform: translateX(0);
            }
            90% {
                opacity: 1;
                transform: translateX(0);
            }
            100% {
                opacity: 0;
                transform: translateX(100%);
            }
        }

        /* Loading overlay */
        #loading-overlay {
            display: none;
            position: fixed;
            z-index: 100;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.4);
            align-items: center;
            justify-content: center;
        }

        #loading-container {
            background-color: white;
            width: 280px;
            height: 180px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 15px;
            box-sizing: border-box;  
            text-align: center; 
        }

        #loading-spinner {
            border: 6px solid #f3f3f3;
            border-top: 6px solid #1e3c72;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin-bottom: 10px;
        }

        #loading-message {
            font-size: 16px;
            color: #1e3c72;
            font-weight: 600;
        }

        @keyframes spin {
            0% { transform: rotate(0deg);}
            100% { transform: rotate(360deg);}
        }

        /* Responsive - make it more mobile friendly */
        @media (max-width: 600px) {
            .content {
                padding: 20px 15px;
                margin-top: 30px;
            }

            .content h1 {
                font-size: 24px;
            }

            form input[type="submit"] {
                padding: 12px;
                font-size: 15px;
            }

            .flash-messages li {
                min-width: 200px;
                padding: 12px 20px;
                font-size: 13px;
            }

            #loading-container {
                width: 180px;
                height: 110px;
                padding: 12px;
            }

            #loading-spinner {
                width: 40px;
                height: 40px;
                margin-bottom: 8px;
            }

            #loading-message {
                font-size: 14px;
            }
        }
    </style>
</head>
<body>
    <h1>URL ScannerRR</h1>
    <!-- Show login/register or logout depending on session -->
    {% if session.username %}
        <p>Welcome, {{ session.username }}</p>   
    {% endif %}
    <form method="post" action="/result" id="scan-form">
        <label for="url">Enter URL to scan:</label>
        <input type="text" name="url" id="url" size="60" required />
        {% with messages = get_flashed_messages() %}
        {% if messages %}
          <ul class="flash-messages" id="flash-messages">
            {% for message in messages %}
              <li>{{ message }}</li>
            {% endfor %}
          </ul>
        {% endif %}
      {% endwith %}
        <input type="submit" value="Scan URL" />
    </form>

    <div id="loading-overlay">
        <div id="loading-container">
            <div id="loading-spinner"></div>
            <div id="loading-message">\</div>
        </div>
    </div>

    <script>
        // Remove flash message on click
        document.addEventListener('DOMContentLoaded', function() {
            const flashMessages = document.querySelectorAll('.flash-messages li');
            flashMessages.forEach(function(msg) {
                msg.addEventListener('click', function() {
                    msg.style.animation = 'slideOut 0.5s forwards';
                    setTimeout(() => {
                        msg.remove();
                    }, 500);
                });
            });

            // Show loading overlay on form submit and prevent multiple submits
            const form = document.getElementById('scan-form');
            form.addEventListener('submit', function(event) {
                // Show loading overlay
                document.getElementById('loading-overlay').style.display = 'flex';

                // Disable submit button to prevent multiple submits
                const submitButton = form.querySelector('input[type="submit"]');
                submitButton.disabled = true;

                // Loading messages from scanning.py to simulate
                const loadingMessages = [
                    "[INFO] URL has no protocol. Detecting correct protocol...",
                    "[WARNING] Could not determine protocol. Defaulting to HTTPS.",
                    "=> Detected Protocol: HTTPS | Adjusted URL: ...",
                    "[WARNING] Provided protocol (http) may be incorrect. Using detected protocol (https).",
                    "=> Adjusted URL: ..."
                ];

                const loadingMessageElement = document.getElementById('loading-message');
                let messageIndex = 0;

                // Function to update loading message every 2 seconds
                function updateLoadingMessage() {
                    if (messageIndex < loadingMessages.length) {
                        loadingMessageElement.textContent = loadingMessages[messageIndex];
                        messageIndex++;
                        setTimeout(updateLoadingMessage, 2000);
                    }
                }

                // Start updating loading messages
                updateLoadingMessage();
            });
        });
    </script>
</body>
</html>
{% endblock %}
